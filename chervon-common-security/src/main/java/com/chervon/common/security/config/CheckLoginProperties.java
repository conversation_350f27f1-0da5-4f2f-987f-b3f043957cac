package com.chervon.common.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 21:18
 */
@Data
@ConfigurationProperties(prefix = "check.login")
@RefreshScope
public class CheckLoginProperties {

    private List<String> excludeUrls;
}

