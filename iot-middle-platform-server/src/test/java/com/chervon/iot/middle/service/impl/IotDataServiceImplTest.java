package com.chervon.iot.middle.service.impl;

import cn.hutool.json.JSONObject;
import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;
import com.chervon.iot.middle.mapper.IotDataMapper;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import com.chervon.common.redis.utils.RedisUtils;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IotDataServiceImplTest {

    // 测试常量
    private static final String TEST_DEVICE_ID = "device001";
    private static final String TEST_PRODUCT_KEY = "product001";
    private static final String TEST_TEMPERATURE_IDENTIFIER = "temperature";
    private static final String TEST_TEMPERATURE_NAME = "温度";
    private static final Double TEST_TEMPERATURE_VALUE = 25.5;
    private static final String TEST_BATTERY_DATA = "battery_12345_end";
    private static final String EXPECTED_BATTERY_RESULT = "12345";

    @Mock
    private AwsIotService awsIotService;

    @Mock
    private IotDataMapper iotDataMapper;

    @Mock
    private IotThingModelIdentifierService iotThingModelIdentifierService;

    @InjectMocks
    private IotDataServiceImpl iotDataService;

    @Nested
    @DisplayName("设备日志保存测试")
    class SaveReportedLogTests {

        @Test
        @DisplayName("正常保存设备日志")
        void shouldSaveReportedLogSuccessfully() {
            // Given
            String dataType = DataConstant.STATUS_DATA_TYPE;
            String deviceId = "device001";
            JSONObject reported = new JSONObject();
            reported.put("deviceId", deviceId);
            reported.put("timestamp", System.currentTimeMillis());
            JSONObject reportedData = new JSONObject();
            reportedData.put("temperature", 25.5);
            reported.put("reported", reportedData);

            String productKey = "product001";
            Set<String> columns = new HashSet<>();
            columns.add("idf_temperature");
            List<IotThingModelIdentifier> identifiers = Arrays.asList(
                    createIdentifier("temperature", "温度")
            );

            when(awsIotService.getProductKey(deviceId)).thenReturn(productKey);
            when(iotThingModelIdentifierService.getProductAllIdentifierList(eq(productKey))).thenReturn(identifiers);
            when(iotDataMapper.existedSTable(anyString())).thenReturn(Arrays.asList("table"));
            when(iotDataMapper.existedSubTable(anyString())).thenReturn(Collections.emptyList());

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(iotDataMapper).savePropertyLog(anyString(), anyString(), eq(deviceId), eq(productKey),
                    eq("reported"), eq("value"), eq("now"), any());
        }

        @Test
        @DisplayName("无效数据类型应跳过处理")
        void shouldSkipInvalidDataType() {
            // Given
            String dataType = "INVALID_TYPE";
            String deviceId = "device001";
            JSONObject reported = new JSONObject();

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(awsIotService, never()).getProductKey(anyString());
            verify(iotDataMapper, never()).savePropertyLog(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyString(), anyString(), any());
        }

        @Test
        @DisplayName("空reported数据应跳过处理")
        void shouldSkipEmptyReportedData() {
            // Given
            String dataType = DataConstant.STATUS_DATA_TYPE;
            String deviceId = "device001";
            JSONObject reported = new JSONObject();
            reported.put("deviceId", deviceId);
            reported.put("timestamp", System.currentTimeMillis());

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(iotDataMapper, never()).savePropertyLog(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyString(), anyString(), any());
        }

        @Test
        @DisplayName("当有标识符时应调用createSubTable")
        void shouldCallCreateSubTableWhenIdentifiersExist() {
            // Given
            String dataType = DataConstant.STATUS_DATA_TYPE;
            String deviceId = TEST_DEVICE_ID;
            JSONObject reported = new JSONObject();
            reported.put("deviceId", deviceId);
            reported.put("timestamp", System.currentTimeMillis());
            JSONObject reportedData = new JSONObject();
            reportedData.put(TEST_TEMPERATURE_IDENTIFIER, TEST_TEMPERATURE_VALUE);
            reported.put("reported", reportedData);

            String productKey = TEST_PRODUCT_KEY;
            IotThingModelIdentifier identifier = createIdentifier(TEST_TEMPERATURE_IDENTIFIER, TEST_TEMPERATURE_NAME);

            when(awsIotService.getProductKey(deviceId)).thenReturn(productKey);
            when(iotThingModelIdentifierService.getProductAllIdentifierList(eq(productKey)))
                .thenReturn(Collections.singletonList(identifier));
            when(iotDataMapper.existedSTable(anyString())).thenReturn(Arrays.asList("table"));
            when(iotDataMapper.existedSubTable(anyString())).thenReturn(Collections.emptyList());

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(iotDataMapper).createSubTable(anyString(), anyString(), eq(deviceId), eq(productKey));
        }

        @Test
        @DisplayName("null数据类型应跳过处理")
        void shouldSkipNullDataType() {
            // Given
            String dataType = null;
            String deviceId = TEST_DEVICE_ID;
            JSONObject reported = new JSONObject();

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(awsIotService, never()).getProductKey(anyString());
            verify(iotDataMapper, never()).savePropertyLog(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyString(), anyString(), any());
        }

        @Test
        @DisplayName("null设备ID应跳过处理")
        void shouldSkipNullDeviceId() {
            // Given
            String dataType = DataConstant.STATUS_DATA_TYPE;
            String deviceId = null;
            JSONObject reported = new JSONObject();

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(awsIotService, never()).getProductKey(anyString());
            verify(iotDataMapper, never()).savePropertyLog(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyString(), anyString(), any());
        }

        @Test
        @DisplayName("null reported对象应跳过处理")
        void shouldSkipNullReported() {
            // Given
            String dataType = DataConstant.STATUS_DATA_TYPE;
            String deviceId = TEST_DEVICE_ID;
            JSONObject reported = null;

            // When
            iotDataService.saveReportedLog(dataType, deviceId, reported);

            // Then
            verify(awsIotService, never()).getProductKey(anyString());
            verify(iotDataMapper, never()).savePropertyLog(anyString(), anyString(), anyString(),
                    anyString(), anyString(), anyString(), anyString(), any());
        }
    }

    @Nested
    @DisplayName("超级表创建测试")
    class CreateSTableTests {

        @Test
        @DisplayName("创建新超级表")
        void shouldCreateNewSTable() {
            // Given
            String productKey = "product001";
            IotThingModel iotThingModel = new IotThingModel();
            iotThingModel.setProductKey(productKey);

            Property property = new Property();
            property.setIdentifier("temperature");
            iotThingModel.setProperties(Arrays.asList(property));

            when(iotDataMapper.existedSTable(anyString())).thenReturn(Collections.emptyList());
            when(iotDataMapper.descSTableField(anyString())).thenReturn(Collections.emptyList());

            // When
            iotDataService.createSTable(iotThingModel);

            // Then
            verify(iotDataMapper).createSTable(anyString(), contains("idf_temperature"));
        }

        @Test
        @DisplayName("修改已存在超级表")
        void shouldAlterExistingSTable() {
            // Given
            String productKey = "product001";
            IotThingModel iotThingModel = new IotThingModel();
            iotThingModel.setProductKey(productKey);

            Property property = new Property();
            property.setIdentifier("temperature");
            iotThingModel.setProperties(Arrays.asList(property));

            when(iotDataMapper.existedSTable(anyString())).thenReturn(Arrays.asList("table"));
            when(iotDataMapper.descSTableField(anyString())).thenReturn(Collections.emptyList());

            // When
            iotDataService.createSTable(iotThingModel);

            // Then
            verify(iotDataMapper).alterSTable(anyString(), contains("idf_temperature"));
        }
    }

    @Nested
    @DisplayName("设备影子日志分页查询测试")
    class PageDeviceShadowLogTests {

        @Test
        @DisplayName("正常分页查询设备影子日志")
        void shouldPageDeviceShadowLogSuccessfully() {
            // Given
            ShadowLogPageDto pageDto = new ShadowLogPageDto();
            pageDto.setDeviceId(TEST_DEVICE_ID);
            pageDto.setPageNum(1);
            pageDto.setPageSize(10);

            String productKey = TEST_PRODUCT_KEY;
            Long total = 25L;
            List<HashMap<String, Object>> mapList = Arrays.asList(
                    createLogMap("2024-01-01", "data1"),
                    createLogMap("2024-01-02", "data2")
            );

            when(awsIotService.getProductKeyRedis(eq(TEST_DEVICE_ID))).thenReturn(productKey);
            when(iotDataMapper.countDeviceShadowLog(eq(productKey), eq(TEST_DEVICE_ID))).thenReturn(total);
            when(iotDataMapper.listDeviceShadowLog(eq(productKey), eq(TEST_DEVICE_ID), eq(0L), eq(10L))).thenReturn(mapList);

            // When
            PageResult<DeviceShadowLogVo> result = iotDataService.pageDeviceShadowLog(pageDto);

            // Then
            assertNotNull(result, "分页结果不能为空");
            assertEquals(1, result.getPageNum(), "页码应该是1");
            assertEquals(10, result.getPageSize(), "页大小应该是10");
            assertEquals(25L, result.getTotal(), "总数应该是25");
            assertEquals(3L, result.getPages(), "总页数应该是3");
            assertEquals(2, result.getList().size(), "返回列表大小应该是2");

            // 验证列表内容不为空
            assertNotNull(result.getList().get(0), "第一个日志对象不能为空");
            assertNotNull(result.getList().get(1), "第二个日志对象不能为空");

            // 验证Mock调用
            verify(awsIotService).getProductKeyRedis(eq(TEST_DEVICE_ID));
            verify(iotDataMapper).countDeviceShadowLog(eq(productKey), eq(TEST_DEVICE_ID));
            verify(iotDataMapper).listDeviceShadowLog(eq(productKey), eq(TEST_DEVICE_ID), eq(0L), eq(10L));
        }

        @Test
        @DisplayName("无数据时返回空分页结果")
        void shouldReturnEmptyPageWhenNoData() {
            // Given
            ShadowLogPageDto pageDto = new ShadowLogPageDto();
            pageDto.setDeviceId("device001");
            pageDto.setPageNum(1);
            pageDto.setPageSize(10);

            String productKey = "product001";
            when(awsIotService.getProductKeyRedis("device001")).thenReturn(productKey);
            when(iotDataMapper.countDeviceShadowLog(eq(productKey), eq("device001"))).thenReturn(0L);

            // When
            PageResult<DeviceShadowLogVo> result = iotDataService.pageDeviceShadowLog(pageDto);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(0L, result.getTotal());
            assertEquals(0L, result.getPages());
            assertEquals(0, result.getList().size());
        }
    }

    @Nested
    @DisplayName("设备拓扑分页查询测试")
    class PageDeviceTopologyTests {

        @Test
        @DisplayName("正常分页查询设备拓扑")
        void shouldPageDeviceTopologySuccessfully() {
            // Given
            TopologyPageDto pageDto = new TopologyPageDto();
            pageDto.setDeviceId("device001");
            pageDto.setPageNum(1);
            pageDto.setPageSize(10);

            String productKey = "product001";
            Long total = 15L;
            List<DeviceTopologyVo> topologyList = Arrays.asList(
                    createTopologyVo("topology1"),
                    createTopologyVo("topology2")
            );

            when(awsIotService.getProductKeyRedis(eq("device001"))).thenReturn(productKey);
            when(iotDataMapper.countDeviceTopology(eq(productKey), eq("device001"), eq(isNull()),
                    eq(isNull()), eq(isNull()), eq(isNull()))).thenReturn(total);
            when(iotDataMapper.listDeviceTopology(eq(productKey), eq("device001"), eq(isNull()),
                    eq(isNull()), eq(isNull()), eq(isNull()), eq(0L), eq(10))).thenReturn(topologyList);

            // When
            PageResult<DeviceTopologyVo> result = iotDataService.pageDeviceTopology(pageDto);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(15L, result.getTotal());
            assertEquals(2L, result.getPages());
            assertEquals(2, result.getList().size());
        }

        @Test
        @DisplayName("带拓扑设备ID过滤的分页查询")
        void shouldPageDeviceTopologyWithFilter() {
            // Given
            TopologyPageDto pageDto = new TopologyPageDto();
            pageDto.setDeviceId("device001");
            pageDto.setTopologyDeviceId("topo001");
            pageDto.setPageNum(1);
            pageDto.setPageSize(10);

            String productKey = "product001";
            when(awsIotService.getProductKeyRedis(eq("device001"))).thenReturn(productKey);
            when(iotDataMapper.countDeviceTopology(eq(productKey), eq("device001"), eq("%topo001%"),
                    eq(isNull()), eq(isNull()), eq(isNull()))).thenReturn(0L);

            // When
            PageResult<DeviceTopologyVo> result = iotDataService.pageDeviceTopology(pageDto);

            // Then
            assertNotNull(result);
            assertEquals(0L, result.getTotal());
            verify(iotDataMapper).countDeviceTopology(eq(productKey), eq("device001"), 
                    eq("%topo001%"), isNull(), isNull(), isNull());
            verify(iotDataMapper).listDeviceTopology(eq(productKey), eq("device001"), eq("topo001"),
                    isNull(), isNull(), isNull(), anyInt(), anyInt());
        }
    }

    @Nested
    @DisplayName("获取挂载电池包测试")
    class GetMountedBatteryTests {

        @Test
        @DisplayName("正常获取挂载电池包ID")
        void shouldGetMountedBatterySuccessfully() {
            // Given
            String productKey = TEST_PRODUCT_KEY;
            String deviceId = TEST_DEVICE_ID;
            String batteryData = TEST_BATTERY_DATA;

            when(iotDataMapper.getMountedBattery(eq(productKey), eq(deviceId))).thenReturn(batteryData);

            // When
            String result = iotDataService.getMountedBattery(productKey, deviceId);

            // Then
            assertEquals(EXPECTED_BATTERY_RESULT, result);
            verify(iotDataMapper).getMountedBattery(eq(productKey), eq(deviceId));
        }

        @Test
        @DisplayName("数据长度不足时返回null")
        void shouldReturnNullWhenDataTooShort() {
            // Given
            String productKey = "product001";
            String deviceId = "device001";
            String batteryData = "short";

            when(iotDataMapper.getMountedBattery(eq(productKey), eq(deviceId))).thenReturn(batteryData);

            // When
            String result = iotDataService.getMountedBattery(productKey, deviceId);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("数据为空时返回null")
        void shouldReturnNullWhenDataEmpty() {
            // Given
            String productKey = "product001";
            String deviceId = "device001";

            when(iotDataMapper.getMountedBattery(eq(productKey), eq(deviceId))).thenReturn("");

            // When
            String result = iotDataService.getMountedBattery(productKey, deviceId);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("SQL异常时返回null")
        void shouldReturnNullWhenSQLException() {
            // Given
            String productKey = "product001";
            String deviceId = "device001";

            when(iotDataMapper.getMountedBattery(eq(productKey), eq(deviceId))).thenReturn(null);

            // When
            String result = iotDataService.getMountedBattery(productKey, deviceId);

            // Then
            assertNull(result);
        }
    }

    @Nested
    @DisplayName("表存在性检查测试")
    class ExistedTableTests {

        @Test
        @DisplayName("表存在时返回true")
        void shouldReturnTrueWhenTableExists() {
            // Given
            String deviceId = TEST_DEVICE_ID;
            String productKey = TEST_PRODUCT_KEY;

            when(awsIotService.getProductKeyRedis(eq(deviceId))).thenReturn(productKey);
            when(iotDataMapper.existedSubTable(eq(anyString()))).thenReturn(Arrays.asList("table"));

            // When
            boolean result = iotDataService.existedTable(deviceId);

            // Then
            assertTrue(result);
            verify(awsIotService).getProductKeyRedis(eq(deviceId));
            verify(iotDataMapper).existedSubTable(anyString());
        }

        @Test
        @DisplayName("表不存在时返回false")
        void shouldReturnFalseWhenTableNotExists() {
            // Given
            String deviceId = "device001";
            String productKey = "product001";

            when(awsIotService.getProductKeyRedis(eq(deviceId))).thenReturn(productKey);
            when(iotDataMapper.existedSubTable(eq(anyString()))).thenReturn(Collections.emptyList());

            // When
            boolean result = iotDataService.existedTable(deviceId);

            // Then
            assertFalse(result);
        }

        @Test
        @DisplayName("产品Key为空时返回false")
        void shouldReturnFalseWhenProductKeyEmpty() {
            // Given
            String deviceId = "device001";

            when(awsIotService.getProductKeyRedis(eq(deviceId))).thenReturn("");

            // When
            boolean result = iotDataService.existedTable(deviceId);

            // Then
            assertFalse(result);
        }
    }

    // 辅助方法
    private IotThingModelIdentifier createIdentifier(String identifier, String name) {
        IotThingModelIdentifier item = new IotThingModelIdentifier();
        item.setIdentifier(identifier);
        item.setName(name);
        return item;
    }

    private HashMap<String, Object> createLogMap(String createTime, String data) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("createTime", createTime);  // 使用传入的参数而不是new Date()
        map.put("data", data);
        return map;
    }

    private DeviceTopologyVo createTopologyVo(String deviceId) {
        DeviceTopologyVo vo = new DeviceTopologyVo();
        vo.setDeviceId(deviceId);
        return vo;
    }
}