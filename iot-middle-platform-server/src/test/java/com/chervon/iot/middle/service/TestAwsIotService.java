package com.chervon.iot.middle.service;

import com.chervon.iot.middle.api.dto.device.IotInsertDeviceDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @Author：flynn.wang
 * @Date：2023/10/18 14:12
 */
@SpringBootTest
public class TestAwsIotService {
    @Autowired
    private AwsIotService awsIotService;
    @Test
    public void testAddStaticGroupDevice() {
        String groupName="TEST_STATIC_GROUP";
        List<String> awsDevicesInGroup = awsIotService.listThingsInGroup(groupName);
        System.out.println(awsDevicesInGroup);
    }
    @Test
    public void testCreateAwsDevice() {
        IotInsertDeviceDto iotInsertDeviceDto=new IotInsertDeviceDto();
        iotInsertDeviceDto.setDeviceId("2");
        awsIotService.createAwsDevice(iotInsertDeviceDto,null);
    }

    @Test
    public void testUpdateThingTypeName(){
        awsIotService.updateAwsDevice("2","1766003336641622018",null);
    }

}
