package com.chervon.iot.middle.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.dto.model.EventDto;
import com.chervon.iot.middle.api.dto.model.PropertyDto;
import com.chervon.iot.middle.api.dto.model.ServiceDto;
import com.chervon.iot.middle.api.enums.ThingModelType;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.pojo.thingmodel.BaseThingModelItem;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.vo.product.IotThingModelDto;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.domain.constant.RedisConstant;
import com.chervon.iot.middle.domain.pojo.IotThingModelIdentifier;
import com.chervon.iot.middle.mapper.IotDataMapper;
import com.chervon.iot.middle.mapper.IotThingModelIdentifierMapper;
import com.chervon.iot.middle.service.IotDataService;
import lombok.extern.slf4j.Slf4j;
import com.chervon.iot.middle.service.IotThingModelIdentifierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @since 2024-07-08 13:58:19
 * @description 物模型标识符服务
 */
@Slf4j
@Service
@DS("second")
public class IotThingModelIdentifierServiceImpl extends
        ServiceImpl<IotThingModelIdentifierMapper, IotThingModelIdentifier> implements IotThingModelIdentifierService {

    @Autowired
    private IotDataService iotDataService;

    private static final String IDENTIFIER_ID = "identifierId:";
    private static final String IDENTIFIER_NAME = "identifierName:";

    @Override
    public Boolean addProperty(@Valid PropertyDto propertyDto) {
        String identifier = propertyDto.getProperty().getIdentifier();

        // 重复物模型Id校验
        if (existIdentifierId(propertyDto.getProductKey(), identifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS,
                    IDENTIFIER_ID + identifier);
        }
        // 重复物模型名称校验
        if (existIdentifierName(propertyDto.getProductKey(), propertyDto.getProperty().getName())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS,
                    IDENTIFIER_NAME + propertyDto.getProperty().getName());
        }

        IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
        modelIdentifier.setProductKey(propertyDto.getProductKey());
        modelIdentifier.setModelType(ThingModelType.PROPERTY.getStatus());
        modelIdentifier.setIdentifier(identifier);
        modelIdentifier.setName(propertyDto.getProperty().getName());
        modelIdentifier.setAccessModel(propertyDto.getProperty().getAccessMode());
        modelIdentifier.setComments(propertyDto.getProperty().getComments());
        modelIdentifier.setDataType(propertyDto.getProperty().getDataType().getType());
        modelIdentifier.setProperties(propertyDto.getProperty().getDataType());
        modelIdentifier.setOutputData(null);
        modelIdentifier.setInputData(null);
        save(modelIdentifier);

        // 时序数据库表增加字段
        iotDataService.addTdIdentifier(propertyDto.getProductKey(), DataConstant.STABLE_IDENTIFIER + identifier,
                propertyDto.getProperty().getDataType().getTdEngineType());

        return true;
    }

    /**
     * 判断是否存在某个物模型id
     *
     * @param productKey
     * @param identifier
     * @return
     */
    @Override
    public boolean existIdentifierId(String productKey, String identifier) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getIdentifier, identifier);
        long count = count(queryWrapper);
        return count > 0;
    }

    /**
     * 判断是否存在某个物模型名称
     *
     * @param productKey
     * @param name
     * @return
     */
    @Override
    public boolean existIdentifierName(String productKey, String name) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getName, name);
        long count = count(queryWrapper);
        return count > 0;
    }

    /**
     * 获取单个标识符
     * 
     * @param productKey
     * @param modelType
     * @param identifier
     * @return
     */
    @Override
    public IotThingModelIdentifier getIdentifier(String productKey, String modelType, String identifier) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, modelType)
                .eq(IotThingModelIdentifier::getIdentifier, identifier);
        final List<IotThingModelIdentifier> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 分页获取identifier列表
     * 
     * @param productKey 产品Id
     * @param modelType  类型
     * @return 分页列表
     */
    public PageResult<IotThingModelIdentifier> getPageIdentifierList(String productKey, String modelType,
            PageRequest pageRequest) {
        // 构建查询条件
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, modelType);
        // 分页请求对象
        Page<IotThingModelIdentifier> pageReq = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        // 分页查询
        final Page<IotThingModelIdentifier> page = page(pageReq, queryWrapper);
        // 构建返回结果
        PageResult<IotThingModelIdentifier> pageResult = new PageResult<>(pageRequest.getPageNum(),
                pageRequest.getPageSize(), page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setList(page.getRecords());
        return pageResult;
    }

    @Override
    public IotThingModel getProductThingModelAll(String productKey) {
        final List<IotThingModelIdentifier> productAllIdentifierList = getProductAllIdentifierList(productKey);
        return appendModelIdentifiers(productKey, productAllIdentifierList);
    }

    /**
     * 获取产品所有identifier列表
     * 
     * @param productKey 产品id
     * @return 分页列表
     */
    @Override
    public List<IotThingModelIdentifier> getProductAllIdentifierList(String productKey) {
        // 构建查询条件
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey);
        return list(queryWrapper);
    }

    private IotThingModel appendModelIdentifiers(String productKey, List<IotThingModelIdentifier> listIdentifier) {
        IotThingModel iotThingModel = new IotThingModel();
        iotThingModel.setProductKey(productKey);
        if (CollectionUtils.isEmpty(listIdentifier)) {
            return iotThingModel;
        }
        final Map<String, List<IotThingModelIdentifier>> groupType = listIdentifier.stream()
                .collect(Collectors.groupingBy(IotThingModelIdentifier::getModelType));
        for (Map.Entry<String, List<IotThingModelIdentifier>> entry : groupType.entrySet()) {
            if (entry.getKey().equals(ThingModelType.PROPERTY.getStatus())) {
                List<Property> propertyList = new ArrayList<>();
                entry.getValue().forEach(identifier -> {
                    Property property = new Property();
                    property.setIdentifier(identifier.getIdentifier());
                    property.setName(identifier.getName());
                    property.setCreateBy(identifier.getCreateBy());
                    property.setUpdateBy(identifier.getUpdateBy());
                    property.setAccessMode(identifier.getAccessModel());
                    property.setComments(identifier.getComments());
                    property.setDataType(identifier.getProperties());
                    propertyList.add(property);
                });
                iotThingModel.setProperties(propertyList);
            } else if (entry.getKey().equals(ThingModelType.EVENT.getStatus())) {
                List<Event> eventList = new ArrayList<>();
                entry.getValue().forEach(identifier -> {
                    Event event = new Event();
                    event.setIdentifier(identifier.getIdentifier());
                    event.setName(identifier.getName());
                    event.setCreateBy(identifier.getCreateBy());
                    event.setUpdateBy(identifier.getUpdateBy());
                    event.setComments(identifier.getComments());
                    event.setType(identifier.getEventType());
                    event.setOutputData(identifier.getOutputData());
                    eventList.add(event);
                });
                iotThingModel.setEvents(eventList);
            } else if (entry.getKey().equals(ThingModelType.SERVICE.getStatus())) {
                List<com.chervon.iot.middle.api.pojo.thingmodel.Service> serviceList = new ArrayList<>();
                entry.getValue().forEach(identifier -> {
                    com.chervon.iot.middle.api.pojo.thingmodel.Service service = new com.chervon.iot.middle.api.pojo.thingmodel.Service();
                    service.setIdentifier(identifier.getIdentifier());
                    service.setName(identifier.getName());
                    service.setCreateBy(identifier.getCreateBy());
                    service.setCreateTime(identifier.getCreateTime());
                    service.setUpdateBy(identifier.getUpdateBy());
                    service.setUpdateTime(identifier.getUpdateTime());
                    service.setComments(identifier.getComments());
                    service.setCallType(identifier.getCallType());
                    service.setIfCallType(identifier.getIfCallType());
                    service.setInputData(identifier.getInputData());
                    service.setOutputData(identifier.getOutputData());
                    serviceList.add(service);
                });
                iotThingModel.setServices(serviceList);
            }
        }
        return iotThingModel;
    }

    private PageResult<Property> convertToPageProperties(PageResult<IotThingModelIdentifier> pageIdentifierResult) {
        // 构建返回结果
        PageResult<Property> pageResult = new PageResult<>(pageIdentifierResult.getPageNum(),
                pageIdentifierResult.getPageSize(), pageIdentifierResult.getTotal());
        pageResult.setPages(pageIdentifierResult.getPages());
        List<Property> list = new ArrayList<>();
        pageIdentifierResult.getList().forEach(identifier -> {
            final Property property = convertIdentifierToProperty(identifier);
            list.add(property);
        });
        pageResult.setList(list);
        return pageResult;
    }

    private Property convertIdentifierToProperty(IotThingModelIdentifier identifier) {
        Property property = new Property();
        property.setIdentifier(identifier.getIdentifier());
        property.setName(identifier.getName());
        property.setCreateBy(identifier.getCreateBy());
        property.setCreateTime(identifier.getCreateTime());
        property.setUpdateBy(identifier.getUpdateBy());
        property.setUpdateTime(identifier.getUpdateTime());
        property.setAccessMode(identifier.getAccessModel());
        property.setComments(identifier.getComments());
        property.setDataType(identifier.getProperties());
        return property;
    }

    private PageResult<Event> convertToPageEvents(PageResult<IotThingModelIdentifier> pageIdentifierResult) {
        // 构建返回结果
        PageResult<Event> pageResult = new PageResult<>(pageIdentifierResult.getPageNum(),
                pageIdentifierResult.getPageSize(), pageIdentifierResult.getTotal());
        pageResult.setPages(pageIdentifierResult.getPages());
        List<Event> list = new ArrayList<>();
        pageIdentifierResult.getList().forEach(identifier -> {
            final Event event = convertIdentifierToEvent(identifier);
            list.add(event);
        });
        pageResult.setList(list);
        return pageResult;
    }

    private Event convertIdentifierToEvent(IotThingModelIdentifier identifier) {
        Event event = new Event();
        event.setIdentifier(identifier.getIdentifier());
        event.setName(identifier.getName());
        event.setCreateBy(identifier.getCreateBy());
        event.setCreateTime(identifier.getCreateTime());
        event.setUpdateBy(identifier.getUpdateBy());
        event.setUpdateTime(identifier.getUpdateTime());
        event.setComments(identifier.getComments());
        event.setType(identifier.getEventType());
        event.setOutputData(identifier.getOutputData());
        return event;
    }

    private PageResult<com.chervon.iot.middle.api.pojo.thingmodel.Service> convertToPageServices(
            PageResult<IotThingModelIdentifier> pageIdentifierResult) {
        // 构建返回结果
        PageResult<com.chervon.iot.middle.api.pojo.thingmodel.Service> pageResult = new PageResult<>(
                pageIdentifierResult.getPageNum(),
                pageIdentifierResult.getPageSize(), pageIdentifierResult.getTotal());
        pageResult.setPages(pageIdentifierResult.getPages());
        List<com.chervon.iot.middle.api.pojo.thingmodel.Service> list = new ArrayList<>();
        pageIdentifierResult.getList().forEach(identifier -> {
            final com.chervon.iot.middle.api.pojo.thingmodel.Service service = convertIdentifierToService(identifier);
            list.add(service);
        });
        pageResult.setList(list);
        return pageResult;
    }

    private com.chervon.iot.middle.api.pojo.thingmodel.Service convertIdentifierToService(
            IotThingModelIdentifier identifier) {
        com.chervon.iot.middle.api.pojo.thingmodel.Service service = new com.chervon.iot.middle.api.pojo.thingmodel.Service();
        service.setIdentifier(identifier.getIdentifier());
        service.setName(identifier.getName());
        service.setCreateBy(identifier.getCreateBy());
        service.setCreateTime(identifier.getCreateTime());
        service.setUpdateBy(identifier.getUpdateBy());
        service.setUpdateTime(identifier.getUpdateTime());
        service.setComments(identifier.getComments());
        service.setCallType(identifier.getCallType());
        service.setIfCallType(identifier.getIfCallType());
        service.setInputData(identifier.getInputData());
        service.setOutputData(identifier.getOutputData());
        return service;
    }

    @Override
    public Boolean editProperty(PropertyDto propertyDto) {

        String identifierId = propertyDto.getProperty().getIdentifier();
        final IotThingModelIdentifier modelIdentifier = getIdentifier(propertyDto.getProductKey(),
                ThingModelType.PROPERTY.getStatus(), identifierId);
        if (Objects.isNull(modelIdentifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_NOT_FOUND, IDENTIFIER_ID + identifierId);
        }
        modelIdentifier.setName(propertyDto.getProperty().getName());
        modelIdentifier.setAccessModel(propertyDto.getProperty().getAccessMode());
        modelIdentifier.setComments(propertyDto.getProperty().getComments());
        modelIdentifier.setDataType(propertyDto.getProperty().getDataType().getType());
        modelIdentifier.setProperties(propertyDto.getProperty().getDataType());
        modelIdentifier.setOutputData(null);
        modelIdentifier.setInputData(null);
        modelIdentifier.setUpdateBy(null);
        updateById(modelIdentifier);
        return true;
    }

    @Override
    public Boolean deleteProperty(String productKey, String identifier) {
        final IotThingModelIdentifier thingModelIdentifier = getIdentifier(productKey,
                ThingModelType.PROPERTY.getStatus(), identifier);
        if (Objects.isNull(thingModelIdentifier)) {
            return true;
        }
        return removeById(thingModelIdentifier.getId());
    }

    @Override
    public PageResult<Property> pageProperties(PageRequest pageRequest, String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.PROPERTY.getStatus(), pageRequest);
        return convertToPageProperties(pageIdentifierList);
    }

    @Override
    public List<Property> listProperties(String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.PROPERTY.getStatus(), new PageRequest());
        return convertToPageProperties(pageIdentifierList).getList();
    }

    @Override
    public Boolean addService(ServiceDto serviceDto) {
        String identifier = serviceDto.getService().getIdentifier();

        if (existIdentifierId(serviceDto.getProductKey(), identifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS,
                    IDENTIFIER_ID + identifier);
        }
        if (existIdentifierName(serviceDto.getProductKey(), serviceDto.getService().getName())) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS,
                    IDENTIFIER_NAME + serviceDto.getService().getName());
        }

        IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
        modelIdentifier.setProductKey(serviceDto.getProductKey());
        modelIdentifier.setModelType(ThingModelType.SERVICE.getStatus());
        modelIdentifier.setIdentifier(identifier);
        modelIdentifier.setName(serviceDto.getService().getName());
        modelIdentifier.setAccessModel(null);
        modelIdentifier.setEventType(null);
        modelIdentifier.setProperties(null);
        modelIdentifier.setComments(serviceDto.getService().getComments());
        if (!CollectionUtils.isEmpty(serviceDto.getService().getOutputData())) {
            if (serviceDto.getService().getOutputData().get(0).getDataType() != null) {
                final String type = serviceDto.getService().getOutputData().get(0).getDataType().getType();
                modelIdentifier.setDataType(type);
            }
        }
        modelIdentifier.setInputData(serviceDto.getService().getInputData());
        modelIdentifier.setOutputData(serviceDto.getService().getOutputData());
        modelIdentifier.setCallType(serviceDto.getService().getCallType());
        modelIdentifier.setIfCallType(serviceDto.getService().getIfCallType());
        save(modelIdentifier);

        // 时序数据库表增加字段
        if (!CollectionUtils.isEmpty(serviceDto.getService().getOutputData()) && 
            serviceDto.getService().getOutputData().get(0).getDataType() != null) {
            iotDataService.addTdIdentifier(serviceDto.getProductKey(), DataConstant.STABLE_IDENTIFIER + identifier,
                    serviceDto.getService().getOutputData().get(0).getDataType().getTdEngineType());
        } else {
            log.warn("Add service {} not have output data!", identifier);
        }

        return true;
    }

    @Override
    public com.chervon.iot.middle.api.pojo.thingmodel.Service getService(
            String productKey, String identifier) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, ThingModelType.SERVICE.getStatus())
                .eq(IotThingModelIdentifier::getIdentifier, identifier);
        final IotThingModelIdentifier one = getOne(queryWrapper);
        if (Objects.isNull(one)) {
            return null;
        }
        return convertIdentifierToService(one);
    }

    @Override
    public Boolean editService(ServiceDto serviceDto) {

        String identifierId = serviceDto.getService().getIdentifier();
        final IotThingModelIdentifier modelIdentifier = getIdentifier(serviceDto.getProductKey(),
                ThingModelType.SERVICE.getStatus(), identifierId);
        if (Objects.isNull(modelIdentifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_NOT_FOUND, IDENTIFIER_ID + identifierId);
        }
        modelIdentifier.setName(serviceDto.getService().getName());
        modelIdentifier.setEventType(null);
        modelIdentifier.setComments(serviceDto.getService().getComments());
        modelIdentifier.setOutputData(serviceDto.getService().getOutputData());
        modelIdentifier.setInputData(serviceDto.getService().getInputData());
        modelIdentifier.setCallType(serviceDto.getService().getCallType());
        modelIdentifier.setIfCallType(serviceDto.getService().getIfCallType());
        modelIdentifier.setUpdateBy(null);
        updateById(modelIdentifier);
        return true;
    }

    @Override
    public Boolean deleteService(String productKey, String identifier) {
        final IotThingModelIdentifier thingModelIdentifier = getIdentifier(productKey,
                ThingModelType.SERVICE.getStatus(), identifier);
        if (Objects.isNull(thingModelIdentifier)) {
            return true;
        }
        return removeById(thingModelIdentifier.getId());
    }

    @Override
    public PageResult<com.chervon.iot.middle.api.pojo.thingmodel.Service> pageServices(
            PageRequest pageRequest, String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.SERVICE.getStatus(), pageRequest);
        return convertToPageServices(pageIdentifierList);
    }

    @Override
    public List<com.chervon.iot.middle.api.pojo.thingmodel.Service> listServices(String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.SERVICE.getStatus(), new PageRequest());
        return convertToPageServices(pageIdentifierList).getList();
    }

    @Override
    public Boolean addEvent(EventDto eventDto) {
        String identifier = eventDto.getEvent().getIdentifier();
        String name = eventDto.getEvent().getName();
        if (existIdentifierId(eventDto.getProductKey(), identifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS,
                    IDENTIFIER_ID + identifier);
        }
        if (existIdentifierName(eventDto.getProductKey(), name)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_ALREADY_EXISTS, IDENTIFIER_NAME + name);
        }

        IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
        modelIdentifier.setProductKey(eventDto.getProductKey());
        modelIdentifier.setModelType(ThingModelType.EVENT.getStatus());
        modelIdentifier.setIdentifier(identifier);
        modelIdentifier.setName(name);
        modelIdentifier.setAccessModel(null);
        modelIdentifier.setEventType(eventDto.getEvent().getType());
        modelIdentifier.setComments(eventDto.getEvent().getComments());
        if (!CollectionUtils.isEmpty(eventDto.getEvent().getOutputData())) {
            if (eventDto.getEvent().getOutputData().get(0).getDataType() != null) {
                final String type = eventDto.getEvent().getOutputData().get(0).getDataType().getType();
                modelIdentifier.setDataType(type);
            }
        }
        modelIdentifier.setProperties(null);
        modelIdentifier.setOutputData(eventDto.getEvent().getOutputData());
        modelIdentifier.setInputData(null);
        save(modelIdentifier);

        // 时序数据库表增加字段
        if (!CollectionUtils.isEmpty(eventDto.getEvent().getOutputData()) && 
            eventDto.getEvent().getOutputData().get(0).getDataType() != null) {
            iotDataService.addTdIdentifier(eventDto.getProductKey(), DataConstant.STABLE_IDENTIFIER + identifier,
                    eventDto.getEvent().getOutputData().get(0).getDataType().getTdEngineType());
        } else {
            log.warn("Add event {} not have output data!", identifier);
        }
        return true;
    }

    @Override
    public Event getEvent(String productKey, String identifier) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, ThingModelType.EVENT.getStatus())
                .eq(IotThingModelIdentifier::getIdentifier, identifier);
        final IotThingModelIdentifier one = getOne(queryWrapper);
        if (Objects.isNull(one)) {
            return null;
        }
        return convertIdentifierToEvent(one);
    }

    @Override
    public Event getSimpleEvent(String productKey, String identifier) {

        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, ThingModelType.EVENT.getStatus())
                .eq(IotThingModelIdentifier::getIdentifier, identifier)
                .select(IotThingModelIdentifier::getIdentifier, IotThingModelIdentifier::getEventType);
        final IotThingModelIdentifier one = getOne(queryWrapper);
        if (Objects.isNull(one)) {
            return null;
        }
        return convertIdentifierToEvent(one);
    }

    @Override
    public Property getProperty(String productKey, String identifier) {
        LambdaQueryWrapper<IotThingModelIdentifier> queryWrapper = new LambdaQueryWrapper<IotThingModelIdentifier>()
                .eq(IotThingModelIdentifier::getProductKey, productKey)
                .eq(IotThingModelIdentifier::getModelType, ThingModelType.PROPERTY.getStatus())
                .eq(IotThingModelIdentifier::getIdentifier, identifier);
        final IotThingModelIdentifier one = getOne(queryWrapper);
        if (Objects.isNull(one)) {
            return null;
        }
        return convertIdentifierToProperty(one);
    }

    @Override
    public Boolean editEvent(EventDto eventDto) {
        String identifierId = eventDto.getEvent().getIdentifier();
        final IotThingModelIdentifier modelIdentifier = getIdentifier(eventDto.getProductKey(),
                ThingModelType.EVENT.getStatus(), identifierId);
        if (Objects.isNull(modelIdentifier)) {
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.DATA_NOT_FOUND, IDENTIFIER_ID + identifierId);
        }
        modelIdentifier.setName(eventDto.getEvent().getName());
        modelIdentifier.setEventType(eventDto.getEvent().getType());
        modelIdentifier.setComments(eventDto.getEvent().getComments());
        modelIdentifier.setOutputData(eventDto.getEvent().getOutputData());
        modelIdentifier.setUpdateBy(null);
        updateById(modelIdentifier);
        return true;
    }

    @Override
    public Boolean deleteEvent(String productKey, String identifier) {
        final IotThingModelIdentifier thingModelIdentifier = getIdentifier(productKey, ThingModelType.EVENT.getStatus(),
                identifier);
        if (Objects.isNull(thingModelIdentifier)) {
            return true;
        }
        return removeById(thingModelIdentifier.getId());
    }

    @Override
    public PageResult<Event> pageEvents(PageRequest pageRequest, String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.EVENT.getStatus(), pageRequest);
        return convertToPageEvents(pageIdentifierList);
    }

    @Override
    public List<Event> listEvents(String productKey) {
        final PageResult<IotThingModelIdentifier> pageIdentifierList = getPageIdentifierList(productKey,
                ThingModelType.EVENT.getStatus(), new PageRequest());
        return convertToPageEvents(pageIdentifierList).getList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addThingModel(IotThingModelDto iotThingModelDto) {
        final String productKey = iotThingModelDto.getProductKey();
        List<IotThingModelIdentifier> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(iotThingModelDto.getProperties())) {
            for (Property property : iotThingModelDto.getProperties()) {
                IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
                modelIdentifier.setProductKey(iotThingModelDto.getProductKey());
                modelIdentifier.setModelType(ThingModelType.PROPERTY.getStatus());
                modelIdentifier.setIdentifier(property.getIdentifier());
                modelIdentifier.setName(property.getName());
                modelIdentifier.setAccessModel(property.getAccessMode());
                modelIdentifier.setComments(property.getComments());
                modelIdentifier.setDataType(property.getDataType().getType());
                modelIdentifier.setProperties(property.getDataType());
                modelIdentifier.setOutputData(null);
                modelIdentifier.setInputData(null);
                list.add(modelIdentifier);
            }
        }
        if (!CollectionUtils.isEmpty(iotThingModelDto.getServices())) {
            for (com.chervon.iot.middle.api.pojo.thingmodel.Service service : iotThingModelDto.getServices()) {
                IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
                modelIdentifier.setProductKey(iotThingModelDto.getProductKey());
                modelIdentifier.setModelType(ThingModelType.SERVICE.getStatus());
                modelIdentifier.setIdentifier(service.getIdentifier());
                modelIdentifier.setName(service.getName());
                modelIdentifier.setAccessModel(null);
                modelIdentifier.setEventType(null);
                modelIdentifier.setProperties(null);
                modelIdentifier.setComments(service.getComments());
                if (!CollectionUtils.isEmpty(service.getOutputData())) {
                    if (service.getOutputData().get(0).getDataType() != null) {
                        final String type = service.getOutputData().get(0).getDataType().getType();
                        modelIdentifier.setDataType(type);
                    }
                }
                modelIdentifier.setInputData(service.getInputData());
                modelIdentifier.setOutputData(service.getOutputData());
                modelIdentifier.setCallType(service.getCallType());
                modelIdentifier.setIfCallType(service.getIfCallType());
                list.add(modelIdentifier);
            }
        }
        if (!CollectionUtils.isEmpty(iotThingModelDto.getEvents())) {
            for (Event event : iotThingModelDto.getEvents()) {
                IotThingModelIdentifier modelIdentifier = new IotThingModelIdentifier();
                modelIdentifier.setProductKey(iotThingModelDto.getProductKey());
                modelIdentifier.setModelType(ThingModelType.EVENT.getStatus());
                modelIdentifier.setIdentifier(event.getIdentifier());
                modelIdentifier.setName(event.getName());
                modelIdentifier.setAccessModel(null);
                modelIdentifier.setEventType(event.getType());
                modelIdentifier.setComments(event.getComments());
                if (!CollectionUtils.isEmpty(event.getOutputData())) {
                    if (event.getOutputData().get(0).getDataType() != null) {
                        final String type = event.getOutputData().get(0).getDataType().getType();
                        modelIdentifier.setDataType(type);
                    }
                }
                modelIdentifier.setProperties(null);
                modelIdentifier.setOutputData(event.getOutputData());
                modelIdentifier.setInputData(null);
                list.add(modelIdentifier);
            }
        }
        // 删除已有物模型
        removeByProductKey(iotThingModelDto.getProductKey());
        // 批量保存
        saveBatch(list);
        // 时序数据库表增加字段
        for (IotThingModelIdentifier modelIdentifier : list) {
            String tdEngineType = getString(modelIdentifier);
            iotDataService.addTdIdentifier(productKey, DataConstant.STABLE_IDENTIFIER + modelIdentifier.getIdentifier(), tdEngineType);
        }

        return true;
    }

    /**
     * 获取物模型字段类型
     * @param modelIdentifier 物模型字段
     * @return 字段类型
     */
    private static String getString(IotThingModelIdentifier modelIdentifier) {
        String tdEngineType = "BINARY(256)";
        if (ThingModelType.PROPERTY.getStatus().equals(modelIdentifier.getModelType()) && 
            modelIdentifier.getProperties() != null) {
            tdEngineType = modelIdentifier.getProperties().getTdEngineType();
        } else if ((ThingModelType.SERVICE.getStatus().equals(modelIdentifier.getModelType()) || 
                   ThingModelType.EVENT.getStatus().equals(modelIdentifier.getModelType())) && 
                  !CollectionUtils.isEmpty(modelIdentifier.getOutputData()) && 
                  modelIdentifier.getOutputData().get(0).getDataType() != null) {
            tdEngineType = modelIdentifier.getOutputData().get(0).getDataType().getTdEngineType();
        } else {
            log.warn("Add thing model {} not have output data!", modelIdentifier.getIdentifier());
        }
        return tdEngineType;
    }

    private void removeByProductKey(String productKey) {
        remove(Wrappers.<IotThingModelIdentifier>lambdaQuery()
                .eq(IotThingModelIdentifier::getProductKey, productKey));
    }

    @Override
    public List<BaseThingModelItem> listThingModelIdentifiersLikeName(Long productId, String name) {
        List<IotThingModelIdentifier> iotThingModelIdentifiers = list(Wrappers.<IotThingModelIdentifier>lambdaQuery()
                .eq(IotThingModelIdentifier::getProductKey, productId)
                .like(IotThingModelIdentifier::getName, name));

        return BeanCopyUtils.copyList(iotThingModelIdentifiers, BaseThingModelItem.class);
    }

    @Override
    public BaseThingModelItem getThingModelByIdentifier(Long productId, String identifier) {
        IotThingModelIdentifier iotThingModelIdentifier = getOne(Wrappers.<IotThingModelIdentifier>lambdaQuery()
                .eq(IotThingModelIdentifier::getProductKey, productId)
                .eq(IotThingModelIdentifier::getIdentifier, identifier), false);
        return BeanCopyUtils.copy(iotThingModelIdentifier, BaseThingModelItem.class);
    }
}