package com.chervon.iot.middle.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.mybatis.util.JsonSqlUtils;
import com.chervon.iot.middle.api.dto.log.LogQueryDto;
import com.chervon.iot.middle.api.vo.log.LogResultVo;
import com.chervon.iot.middle.domain.pojo.DeviceLog;
import com.chervon.iot.middle.mapper.DeviceLogMapper;
import com.chervon.iot.middle.service.DeviceLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 设备日志记录表服务接口实现
 *
 * <AUTHOR>
 * @since 2024-11-06 18:21:29
 * @description 设备日志记录表服务接口实现
 */
@Slf4j
@RequiredArgsConstructor
@DS("second")
@Service
public class DeviceLogServiceImpl extends ServiceImpl<DeviceLogMapper, DeviceLog> implements DeviceLogService {
    @Override
    public PageResult<LogResultVo> getAppDeviceLog(LogQueryDto queryDto) {
        if(com.chervon.common.core.utils.StringUtils.isEmpty(queryDto.getDeviceId()) && Objects.isNull(queryDto.getLogTemplateId())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"deviceId/logTemplateId");
        }
        IPage<DeviceLog> pageRequest= new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        LambdaQueryWrapper<DeviceLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(queryDto.getDeviceId()),DeviceLog::getDeviceId, queryDto.getDeviceId())
                .eq(!Objects.isNull(queryDto.getLogTemplateId()), DeviceLog::getLogTemplateId, queryDto.getLogTemplateId())
                .eq(!Objects.isNull(queryDto.getRuleId()), DeviceLog::getRuleId, queryDto.getRuleId())
                .like(!CollectionUtils.isEmpty(queryDto.getListLogTemplateId()),DeviceLog::getLogTemplateId,queryDto.getListLogTemplateId());
        if(StringUtils.isNotEmpty(queryDto.getCreateStartTime())) {
            long beginTime = convertToTimestamp(queryDto.getCreateStartTime());
            if (beginTime > 0) {
                queryWrapper.ge(DeviceLog::getCreateTime, beginTime);
            }
        }
        if(StringUtils.isNotEmpty(queryDto.getCreateEndTime())) {
            long endTime = convertToTimestamp(queryDto.getCreateEndTime());
            if (endTime > 0) {
                queryWrapper.lt(DeviceLog::getCreateTime, endTime);
            }
        }
        queryWrapper.orderByDesc(DeviceLog::getTs);
        final IPage<DeviceLog> page = this.page(pageRequest, queryWrapper);
        final List<LogResultVo> logResultVos = BeanCopyUtils.copyList(page.getRecords(), LogResultVo.class);
        PageResult<LogResultVo> pageResult= new PageResult<>(queryDto.getPageNum(),queryDto.getPageSize());
        pageResult.setList(logResultVos);
        pageResult.setPages(page.getPages());
        pageResult.setTotal(page.getTotal());
        return pageResult;
    }

    /**
     * 将日期字符串转换为时间戳
     *
     * @param dateString 日期字符串，格式为 yyyy-MM-dd
     * @return 时间戳（毫秒），如果格式错误则返回 -1
     */
    public static long convertToTimestamp(String dateString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setLenient(false); // 严格解析日期
        try {
            Date date = dateFormat.parse(dateString);
            return date.getTime();
        } catch (ParseException e) {
            return 0;
        }
    }
}