package com.chervon.iot.middle.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisRepository;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.middle.api.dto.log.DeviceLogParamDto;
import com.chervon.iot.middle.api.dto.log.LogPageDto;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.exception.IotMiddleErrorCode;
import com.chervon.iot.middle.api.pojo.thingmodel.Event;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.pojo.thingmodel.Property;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import com.chervon.iot.middle.config.ExceptionMessageUtil;
import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.domain.constant.RedisConstant;
import com.chervon.iot.middle.domain.dto.DeviceLogDto;
import com.chervon.iot.middle.mapper.IotDataMapper;
import com.chervon.iot.middle.service.AwsIotService;
import com.chervon.iot.middle.service.IotDataService;
import com.chervon.iot.middle.service.data_analysis.IHandlerManager;
import com.chervon.iot.middle.service.data_analysis.entity.ContextAttributes;
import com.chervon.iot.middle.service.data_analysis.entity.DeviceInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @className IotDataServiceImpl
 * @description 时序数据接口实现
 * @date 2022/5/16 16:37
 */
@Service
@Slf4j
public class IotDataServiceImpl implements IotDataService {

    @Autowired
    private AwsIotService awsIotService;

    @Autowired
    private RedisRepository redisRepository;

    @Autowired
    private IotDataMapper iotDataMapper;

    /**
     * 设备上报通用日志记录
     *
     * @param deviceId 设备ID
     * @param reported 上报数据
     */
    @Async
    @Override
    public void saveReportedLog(String dataType, String deviceId, Object reported) {
        try {
            JSONObject jsonObject = JSONUtil.parseObj(reported);
            if (!jsonObject.containsKey(DataConstant.REPORTED)) {
                log.warn("device log is empty or is an error log: {}", jsonObject);
                return;
            }
            JSONObject reportedObject = jsonObject.getJSONObject(DataConstant.REPORTED);
            Long timestamp = jsonObject.getLong(DataConstant.TIMESTAMP);
            String productKey = awsIotService.getProductKey(deviceId);
            String stPrefix;
            String tPrefix;
            String cacheKeyPrefix;
            if (dataType.equals(DataConstant.STATUS_DATA_TYPE)) {
                stPrefix = DataConstant.STABLE_PRODUCT_LOG;
                tPrefix = DataConstant.STABLE_SUB_PRODUCT_LOG;
                cacheKeyPrefix = RedisConstant.IOT_PRODUCT_ID_KEY;
            } else if (dataType.equals(DataConstant.STATUS_DATA_TYPE_EXP)) {
                stPrefix = DataConstant.STABLE_PRODUCT_LOG_EXP;
                tPrefix = DataConstant.SUB_TABLE_PRODUCT_LOG_EXP;
                cacheKeyPrefix = RedisConstant.IOT_PRODUCT_ID_KEY_EXP;
            } else {
                log.warn("device data type is invalid: {}", dataType);
                return;
            }
            // 处理上报时间
            SimpleDateFormat sd = new SimpleDateFormat(DataConstant.YYYY_MM_DD_HH_MM_SS_SSS);
            Date dt = sd.parse(sd.format(new Date(timestamp)));
            // 获取目前已维护的物模型ID
            String cacheKey = cacheKeyPrefix + productKey;
            Set<String> columns = RedisUtils.getCacheSet(cacheKey);
            // 处理上报数据，拼接入库字段
            Map<String, String> reportedKeyAndValueList = handleReportedKeyAndValueList(reportedObject, columns, deviceId);
            String keyList = reportedKeyAndValueList.get(DataConstant.KEY_LIST);
            String valueList = reportedKeyAndValueList.get(DataConstant.VALUE_LIST);
            // 处理数据存储，若无超级表则数据存储流程结束，若有超级表无设备子表则创建设备子表
            String stableName = stPrefix + productKey;
            String subTableName = tPrefix + productKey + "_" + deviceId;
            boolean isExisted = handleTable(productKey, deviceId, stableName, subTableName);
            if (!isExisted && !keyList.isEmpty() && !valueList.isEmpty()) {
                // 保存日志
                iotDataMapper.savePropertyLog(stableName, subTableName, deviceId, productKey, keyList, valueList, JSONUtil.toJsonStr(reported), dt);
            }
            //公共拦截器处理
            processBusinessHandler(deviceId,productKey,reportedObject,timestamp);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 判断完善设备数据存储超级表及子表结构
     *
     * @param productKey     产品ID
     * @param deviceId       设备ID
     * @param stableName     超级表
     * @param subTableName   子表
     */
    private boolean handleTable(String productKey, String deviceId, String stableName, String subTableName) {
        // 判断设备上报日志表是否存在
        List<Object> sTables = iotDataMapper.existedSTable(stableName);
        List<Object> subTables = iotDataMapper.existedSubTable(subTableName);
        // 若无超级表则数据存储流程结束
        if (sTables.isEmpty()) {
            log.warn("Stable [product_{}] is not exists!", productKey);
            return false;
        }
        // 有超级表无对应子表，则创建子表
        if (subTables.isEmpty()) {
            iotDataMapper.createSubTable(stableName, subTableName, deviceId, productKey);
        }
        return true;
    }

    /**
     * 处理上报的键值对列表
     * @param reportedObject 上报的对象
     * @param deviceId 设备ID
     * @return 处理后的key-value映射
     */
    private Map<String, String> handleReportedKeyAndValueList(JSONObject reportedObject, Set<String> hadIds, String deviceId) {
        if (reportedObject == null || reportedObject.isEmpty()) {
            return Collections.emptyMap();
        }
        StringBuilder keyListBuilder = new StringBuilder();
        StringBuilder valueListBuilder = new StringBuilder();
        for (String key : reportedObject.keySet()) {
            // 过滤未维护的物模型ID及对应数据不入库
            if (!hadIds.contains(DataConstant.STABLE_IDENTIFIER + key)) {
                continue;
            }
            // 获取值
            Object value = reportedObject.get(key);
            // 处理同步服务
            processSyncTask(key, value, deviceId);
            // 处理不同类型的数据
            if (DataConstant.STABLE_ROUTE.equals(key)) {
                processRouteData(value.toString(), keyListBuilder, valueListBuilder);
            } else if (DataConstant.LOAD_CURVE.equals(key)) {
                processLoadCurveData(value, keyListBuilder, valueListBuilder);
            } else {
                handleReportedValues(key, keyListBuilder, value, valueListBuilder);
            }
        }

        // 构建结果
        Map<String, String> result = new HashMap<>(2);
        result.put(DataConstant.KEY_LIST, keyListBuilder.toString());
        result.put(DataConstant.VALUE_LIST, valueListBuilder.toString());
        return result;
    }

    /**
     * 处理同步任务
     */
    private void processSyncTask(String key, Object value, String deviceId) {
        String taskKey = RedisConstant.IOT_TASK_KEY_PRE + deviceId + ":" + key;
        if (redisRepository.exists(taskKey)) {
            String taskQueueKey = RedisConstant.IOT_TASK_QUEUE_PRE + deviceId + ":" + key;
            redisRepository.leftPush(taskQueueKey, value);
        }
    }

    /**
     * 处理路由数据
     */
    private void processRouteData(String route, StringBuilder keyListBuilder, StringBuilder valueListBuilder) {
        keyListBuilder.append("route_value,");
        valueListBuilder.append("'").append(route).append("',");

        String[] devices = route.split(DataConstant.STABLE_ROUTE_SPLIT);
        for (int index = 0; index < devices.length; index++) {
            processDeviceRoute(devices, index, keyListBuilder, valueListBuilder);
        }
    }

    /**
     * 处理设备路由信息
     */
    private void processDeviceRoute(String[] devices, int index, StringBuilder keyListBuilder, StringBuilder valueListBuilder) {
        // 处理上游设备
        if (index > 0) {
            String[] upString = devices[index - 1].split(DataConstant.STABLE_ROUTE_TYPE_SPLIT);
            String[] currentType = devices[index].split(DataConstant.STABLE_ROUTE_TYPE_SPLIT);

            appendRouteInfo(keyListBuilder, valueListBuilder,
                    "route_up", upString[0],
                    "up_type", currentType.length > 1 ? currentType[1] : null);
        }

        // 处理下游设备
        if (index < devices.length - 1) {
            String[] downString = devices[index + 1].split(DataConstant.STABLE_ROUTE_TYPE_SPLIT);

            appendRouteInfo(keyListBuilder, valueListBuilder,
                    "route_down", downString[0],
                    "down_type", downString.length > 1 ? downString[1] : null);
        }
    }

    /**
     * 处理负载曲线数据
     */
    private void processLoadCurveData(Object loadCurveValues, StringBuilder keyListBuilder, StringBuilder valueListBuilder) {
        long startTime = getLoadCurveStartTime(loadCurveValues);
        if (startTime > 0) {
            handleReportedValues(DataConstant.LOAD_CURVE_START_TIME,
                    keyListBuilder, startTime, valueListBuilder);
        }
        handleReportedValues(DataConstant.LOAD_CURVE,
                keyListBuilder, loadCurveValues, valueListBuilder);
    }

    /**
     * 追加路由信息
     */
    private void appendRouteInfo(StringBuilder keyListBuilder, StringBuilder valueListBuilder,
                                 String idKey, String idValue,
                                 String typeKey, String typeValue) {
        keyListBuilder.append(idKey).append(",").append(typeKey).append(",");
        valueListBuilder.append("'").append(idValue).append("',")
                .append("'").append(typeValue != null ? typeValue : "").append("',");
    }


    /**
     * 描述：获取负载曲线数据的开始时间
     *
     * @param loadCurveValues 负载曲线数据
     * @return long
     * @date 2024/11/14 14:58
     **/
    private long getLoadCurveStartTime(Object loadCurveValues) {
        try {
            JSONArray jsonArray = JSONArray.parseArray(loadCurveValues.toString());
            if (!jsonArray.isEmpty()) {
                com.alibaba.fastjson.JSONObject firstObject = jsonArray.getJSONObject(0);
                long tsMsValue = firstObject.getLong("ts_ms");
                log.info("获取到ts_ms:{}", tsMsValue);
                return tsMsValue;
            } else {
                log.info("获取不到ts_ms");
                return 0L;
            }
        } catch (Exception exception) {
            // 解析异常，没有解析到ts_ms
            return 0L;
        }
    }

    /**
     * 描述：处理上报数据
     *
     * @param key            物模型ID
     * @param value          物模型ID对应的值
     * @param keyListBuilder 物模型ID拼接sql
     * @param valueListBuilder 物模型值拼接sql
     **/
    private void handleReportedValues(String key, StringBuilder keyListBuilder, Object value, StringBuilder valueListBuilder) {
        keyListBuilder.append(DataConstant.STABLE_IDENTIFIER).append(key).append(",");
        if (value instanceof String) {
            valueListBuilder.append("'").append(value).append("'").append(",");
        } else if (JSONUtil.isTypeJSONObject(value.toString())) {
            valueListBuilder.append("'").append(value).append("'").append(",");
        } else if (JSONUtil.isTypeJSONArray(value.toString())) {
            valueListBuilder.append("'").append(value).append("'").append(",");
        } else {
            valueListBuilder.append(value).append(",");
        }
    }


    /**
     * 描述：根据产品物模型属性类建表
     *
     * @param iotThingModel 产品物模型属性类
     * @date 2024/11/14 14:58
     **/
    @Override
    public void createSTable(IotThingModel iotThingModel) {
        String productKey = iotThingModel.getProductKey();
        List<Property> properties = iotThingModel.getProperties();
        List<com.chervon.iot.middle.api.pojo.thingmodel.Service> services = iotThingModel.getServices();
        List<Event> events = iotThingModel.getEvents();
        StringBuilder stringBuilder = new StringBuilder();
        HashSet<String> modelIds = new HashSet<>();

        Optional.ofNullable(properties).orElse(Collections.emptyList()).forEach(property -> {
            String identifier = property.getIdentifier();
            modelIds.add(DataConstant.STABLE_IDENTIFIER + identifier);
        });
        Optional.ofNullable(services).orElse(Collections.emptyList()).forEach(service -> {
            String identifier = service.getIdentifier();
            modelIds.add(DataConstant.STABLE_IDENTIFIER + identifier);
        });
        Optional.ofNullable(events).orElse(Collections.emptyList()).forEach(event -> {
            String identifier = event.getIdentifier();
            modelIds.add(DataConstant.STABLE_IDENTIFIER + identifier);
        });
        String stableName = DataConstant.STABLE_PRODUCT_LOG + productKey;

        // 缓存物模型ID合集
        String cacheKeyPrefix = RedisConstant.IOT_PRODUCT_ID_KEY;
        String cacheKey = cacheKeyPrefix + productKey;
        RedisUtils.setCacheSet(cacheKey, modelIds);

        HashSet<String> tdColumns = new HashSet<>(iotDataMapper.descSTableField(stableName));
        HashSet<String> productIdSet = new HashSet<>(modelIds);
        productIdSet.removeAll(tdColumns);
        // 创建超级表
        List<Object> sTables = iotDataMapper.existedSTable(stableName);
        if (sTables.isEmpty()) {
            for (String id : modelIds) {
                stringBuilder.append(DataConstant.STABLE_IDENTIFIER).append(id).append(" ").append("BINARY(200)").append(",");
            }
            // 无超级表，创建超级表
            iotDataMapper.createSTable(stableName, stringBuilder.substring(0, stringBuilder.length() - 1));
        } else if (!productIdSet.isEmpty()) {
            for (String id : productIdSet) {
                // 有超级表，但管理平台物模型ID有新增，则更新超级表
                iotDataMapper.alterSTable(stableName, id + " BINARY(200)");
            }
        } else {
            log.warn("Table is exists!");
        }
    }

    /**
     * 描述：分页查询设备影子日志
     *
     * @param shadowLogPageDto 查询条件
     * @return PageResult<DeviceShadowLogVo> 分页结果
     * @date 2024/11/14 14:58
     **/
    @Override
    public PageResult<DeviceShadowLogVo> pageDeviceShadowLog(ShadowLogPageDto shadowLogPageDto) {

        String deviceId = shadowLogPageDto.getDeviceId();
        Integer pageNum = shadowLogPageDto.getPageNum();
        Integer pageSize = shadowLogPageDto.getPageSize();

        String productKey = awsIotService.getProductKeyRedis(deviceId);
        Long total = iotDataMapper.countDeviceShadowLog(productKey, deviceId);
        if (total == null || total == 0) {
            PageResult<DeviceShadowLogVo> pageResult = new PageResult<>(pageNum, pageSize, 0);
            pageResult.setPages(0);
            return pageResult;
        }
        long pages = ((long) Math.ceil((double) total / (double) pageSize));
        Integer offset = shadowLogPageDto.getPageSize() * (pageNum - 1);
        List<HashMap<String, Object>> mapList = iotDataMapper.listDeviceShadowLog(productKey, deviceId, offset, pageSize);
        List<DeviceShadowLogVo> resultList = new ArrayList<>();
        mapList.forEach(item -> {
            DeviceShadowLogVo logVo = new DeviceShadowLogVo();
            logVo.setLogTime((Date) item.get("createTime"));
            Map<String, Object> logMap = new HashMap<>(8);
            logMap.putAll(item);
            logVo.setLogMap(logMap);
            resultList.add(logVo);
        });
        PageResult<DeviceShadowLogVo> pageResult = new PageResult<>(pageNum, pageSize, total);
        pageResult.setList(resultList);
        pageResult.setPages(pages);
        return pageResult;
    }

    @Override
    public PageResult<DeviceTopologyVo> pageDeviceTopology(TopologyPageDto topologyPageDto) {
        String deviceId = topologyPageDto.getDeviceId();
        String topologyDeviceId = null;
        if (StringUtils.isNotEmpty(topologyPageDto.getTopologyDeviceId())) {
            topologyDeviceId = "%" + topologyPageDto.getTopologyDeviceId() + "%";
        }
        Integer pageNum = topologyPageDto.getPageNum();
        Integer pageSize = topologyPageDto.getPageSize();
        Date startTime =
                topologyPageDto.getStartTime() == null ? null :
                        new Date(topologyPageDto.getStartTime());
        Date endTime = topologyPageDto.getEndTime() == null ? null :
                new Date(topologyPageDto.getEndTime());
        Integer type = topologyPageDto.getType();

        String productKey = awsIotService.getProductKeyRedis(deviceId);
        Long total = iotDataMapper.countDeviceTopology(productKey, deviceId, topologyDeviceId, startTime, endTime, type);
        if (total == null || total == 0) {
            return new PageResult<>(pageNum, pageSize, 0);
        }
        long pages = (long) Math.ceil((double) total / (double) pageSize);
        Integer offset = pageSize * (pageNum - 1);
        List<DeviceTopologyVo> resultList = iotDataMapper.listDeviceTopology(productKey,
                deviceId, topologyDeviceId, startTime, endTime, type, offset, pageSize);
        PageResult<DeviceTopologyVo> pageResult = new PageResult<>(pageNum, pageSize, total);
        pageResult.setList(resultList);
        pageResult.setPages(pages);
        return pageResult;
    }

    @Override
    public String getLastUpdateLog(String productKey, String deviceId, String identifier, Integer isReported) {
        return iotDataMapper.getLastUpdateLog(productKey, deviceId, identifier, isReported);
    }

    @Override
    public List<HashMap<String, Object>> listDeviceShadowLog(String productKey, String deviceId, long offset, long size) {
        return iotDataMapper.listDeviceShadowLog(productKey, deviceId, offset, size);
    }

    @Override
    public Long countDeviceShadowLog(String productKey, String deviceId) {
        return iotDataMapper.countDeviceShadowLog(productKey, deviceId);
    }

    @Override
    public DeviceLogDto getLatestLog(String productKey, String deviceId) {
        return iotDataMapper.getLatestLog(productKey, deviceId);
    }

    @Override
    public Map<String, Object> getLatestDataByTs(String productKey, String deviceId, String columns, String condition) {
        try {
            return iotDataMapper.getLatestColumnLog(productKey, deviceId, columns, condition);
        } catch (Exception e) {
            log.error("查询时序数据库getLatestDataByTs发生异常,deviceId:{},columns:{},condition:{},error:{}", deviceId, columns, condition, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getLatestDataBy1024(String productKey, String deviceId, String columns, String condition) {
        try {
            return iotDataMapper.getLatestDataBy1024(productKey, deviceId, columns, condition);
        } catch (Exception e) {
            log.error("查询时序数据库getLatestDataBy1024发生异常,deviceId:{},columns:{},condition:{},error:{}", deviceId, columns, condition, e);
            return null;
        }
    }

    @Override
    public List<DeviceLogDto> listDeviceLogs(String productKey, LogPageDto logPageDto, Integer offset, Integer pageSize) {
        return iotDataMapper.listDeviceLogs(productKey, logPageDto, offset, pageSize);
    }

    @Override
    public Long countDeviceTopology(String productKey, String deviceId, String topologyDeviceId, Date startTime, Date endTime, Integer type) {
        return iotDataMapper.countDeviceTopology(productKey, deviceId, topologyDeviceId, startTime, endTime, type);
    }

    @Override
    public List<DeviceTopologyVo> listDeviceTopology(String productKey, String deviceId, String topologyDeviceId, Date startTime, Date endTime, Integer type, long offset, Integer size) {
        return iotDataMapper.listDeviceTopology(productKey, deviceId, topologyDeviceId, startTime, endTime, type, offset, size);
    }

    @Override
    public List<DeviceLogDto> pageDeviceLogs(String productKey, Integer offset, Integer pageSize, LogPageDto logPageDto) {
        return iotDataMapper.pageDeviceLogs(productKey, offset, pageSize, logPageDto);
    }

    @Override
    public List<DeviceLogDto> getDeviceLogs(String productKey, LogPageDto logPageDto) {
        return iotDataMapper.getDeviceLogs(productKey, logPageDto);
    }

    @Override
    public List<DeviceLogDto> getDeviceLogList(String productKey, DeviceLogParamDto deviceLogParamDto) {
        return iotDataMapper.getDeviceLogList(productKey, deviceLogParamDto);
    }

    @Override
    public Long countDeviceLogs(String productKey, LogPageDto logPageDto) {
        return iotDataMapper.countDeviceLogs(productKey, logPageDto);
    }

    /**
     * 获取挂载的电池包
     *
     * @param productKey 产品id
     * @param deviceId   设备id
     * @return 电池包id
     */
    @Override
    public String getMountedBattery(String productKey, String deviceId) {
        try {
            String data = iotDataMapper.getMountedBattery(productKey, deviceId);
            if (StringUtils.isNotEmpty(data) && data.length() > CommonConstant.SEVEN) {
                return data.substring(CommonConstant.SEVEN, data.length() - CommonConstant.ONE);
            }
            return null;
        } catch (UncategorizedSQLException exception) {
            // 没有idf_2019字段，查询异常，表示没有挂载的子设备，返回null
            return null;
        }
    }

    @Override
    public boolean existedTable(String deviceId) {
        String productKey = awsIotService.getProductKeyRedis(deviceId);
        if (StringUtils.isEmpty(productKey)) {
            return false;
        }
        String tableName = DataConstant.STABLE_SUB_PRODUCT_LOG + productKey + StringPool.UNDERSCORE + deviceId;
        List<Object> tables = iotDataMapper.existedSubTable(tableName);
        return !tables.isEmpty();
    }

    /**
     * 异步处理公共业务逻辑
     * @param
     */
    public void processBusinessHandler(String deviceId,String productKey,JSONObject reportedObject,Long timestamp) {
        //执行责任链分发处理流程
        Map handlerContext = new HashMap();
        try {
            DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
            deviceInfoDto.setDeviceId(deviceId);
            deviceInfoDto.setProductId(Long.valueOf(productKey));
            deviceInfoDto.setReportedObject(reportedObject);
            deviceInfoDto.setTimestamp(timestamp);
            handlerContext.put(ContextAttributes.DeviceInfoDto.getCode(), deviceInfoDto);
            IHandlerManager handlerManager= SpringUtils.getBean(IHandlerManager.class);
            handlerManager.start(handlerContext);
        } catch (Exception e) {
            log.error("middle调用责任链拦截处理器业务出错,请求参数：{} 错误信息：{}",handlerContext,e.getMessage(),e);
        }
    }

    /**
     * 时序数据库表增加字段
     *
     * @param productKey PID
     * @param identifier 物模型ID
     * @param idType     数据类型
     */
    @Override
    @DS("tdengine")
    public void addTdIdentifier(String productKey, String identifier, String idType) {
        String cacheKey = RedisConstant.IOT_PRODUCT_ID_KEY + productKey;
        String stableName = DataConstant.STABLE_PRODUCT_LOG + productKey;

        try {
            boolean stableExists = !iotDataMapper.existedSTable(stableName).isEmpty();
            // 检查字段是否已存在（仅当超级表存在时）
            if (stableExists && iotDataMapper.descSTableField(stableName).contains(identifier)) {
                log.info("identifier [{}] already exists in stable [{}]", identifier, stableName);
                Set<String> columns = RedisUtils.getCacheSet(cacheKey);
                columns.add(identifier);
                RedisUtils.setCacheSet(cacheKey, columns);
                return;
            }
            // 更新缓存
            Set<String> columns = RedisUtils.getCacheSet(cacheKey);
            columns.add(identifier);
            RedisUtils.setCacheSet(cacheKey, columns);
            // 创建或修改超级表
            if (stableExists) {
                iotDataMapper.alterSTable(stableName, identifier + " " + idType);
            } else {
                iotDataMapper.createSTable(stableName, identifier + " " + idType);
            }
        } catch (Exception e) {
            log.error("addTdIdentifier error:{}", e.getMessage(), e);
            Set<String> columns = RedisUtils.getCacheSet(cacheKey);
            columns.remove(identifier);
            RedisUtils.setCacheSet(cacheKey, columns);
            throw ExceptionMessageUtil.getException(IotMiddleErrorCode.CREATE_TD_FIELD_ERROR, e.getMessage());
        }
    }

}
