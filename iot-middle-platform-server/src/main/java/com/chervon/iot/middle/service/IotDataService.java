package com.chervon.iot.middle.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.middle.api.dto.log.DeviceLogParamDto;
import com.chervon.iot.middle.api.dto.log.LogPageDto;
import com.chervon.iot.middle.api.dto.log.ShadowLogPageDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo;
import com.chervon.iot.middle.domain.dto.DeviceLogDto;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IotDataService {


    /**
     * 保存设备上报日志
     * <AUTHOR>
     * @date 18:47 2022/7/19
     * @param deviceId:
     * @param reported:
     * @return void
     **/
    void saveReportedLog(String type, String deviceId, Object reported);

    /**
     * 创建超级表
     * <AUTHOR>
     * @date 11:16 2022/7/20
     * @param iotThingModel: 物模型数据
     * @return void
     **/
    void createSTable(IotThingModel iotThingModel);


    /**
     * 判断表是否存在
     **/
    boolean existedTable(String deviceId);

    /**
     * 分页获取设备影子历史记录
     * <AUTHOR>
     * @date 19:15 2022/7/21
     * @param shadowLogPageDto:
     * @return com.chervon.common.core.domain.PageResult
     * <com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo>
     **/
    PageResult<DeviceShadowLogVo> pageDeviceShadowLog(ShadowLogPageDto shadowLogPageDto);

    /**
     * 分页获取设备拓扑信息列表
     * <AUTHOR>
     * @date 15:04 2022/7/25
     * @param topologyPageDto:
     * @return com.chervon.common.core.domain.PageResult<com.chervon.iot.middle.api.vo.device.DeviceTopologyVo>
     **/
    PageResult<DeviceTopologyVo> pageDeviceTopology(TopologyPageDto topologyPageDto);


    /**
     * 查询设备最近一次更新日志
     * @param productKey:
     * @param deviceId: 设备id
     * @param identifier:
     * @param isReported: true 上报 false 下发 null 不区分
     * @return java.lang.String
     **/
    String getLastUpdateLog(String productKey, String deviceId, String identifier, Integer isReported);

    /**
     * 获取设备影子列表
     * @param deviceId: 设备id
     * @param offset: 偏移量
     * @param size: 大小
     * @return java.util.List<com.chervon.iot.middle.api.vo.log.DeviceShadowLogVo>
     **/
    List<HashMap<String, Object>> listDeviceShadowLog(String productKey, String deviceId, long offset, long size);

    /**
     * 获取设备影子历史数量
     * <AUTHOR>
     * @date 9:38 2022/7/22
     * @param deviceId:
     * @return java.lang.Long
     **/
    Long countDeviceShadowLog(String productKey, String deviceId);


    /**
     * 获取设备最近一次的日志
     * @param deviceId: 设备id
     * @return com.chervon.iot.middle.domain.dto.DeviceLogDto
     **/
    DeviceLogDto getLatestLog(String productKey, String deviceId);

    /**
     * 获取指定列最后一条记录
     * @param productKey
     * @param deviceId
     * @param columns
     * @return
     */
    Map<String,Object> getLatestDataByTs(String productKey, String deviceId, String columns, String condition);

    /**
     * 获取指定时间范围内最后一条数据值（order by idf_1024 desc)
     * @param productKey 产品id
     * @param deviceId 设备id
     * @param columns 指定查询的列值
     * @param condition 过滤条件
     * @return 指定列的最后一条数据值
     */
    Map<String,Object> getLatestDataBy1024(String productKey, String deviceId,String columns,String condition);

    /**
     * 获取设备日志列表
     * @param offset: 偏移量
     * @param pageSize: 分页大小
     * @return java.util.List<com.chervon.iot.middle.domain.dto.DeviceLogDto>
     **/
    List<DeviceLogDto> listDeviceLogs(String productKey, LogPageDto logPageDto, Integer offset, Integer pageSize);

    /**
     * 获取拓扑记录总数
     * @param deviceId: 设备id
     * @param startTime: 开始时间
     * @param endTime: 结束时间
     * @param type: 数据上下行类型 0上行 1下行
     * @return java.lang.Long
     **/
    Long countDeviceTopology(String productKey, String deviceId, String topologyDeviceId, Date startTime, Date endTime,
                            Integer type);

    /**
     * 获取拓扑记录
     * @param deviceId: 设备id
     * @param startTime: 开始时间
     * @param endTime: 结束时间
     * @param type: 数据上下行类型 0上行 1下行
     * @param offset: 偏移量
     * @param size: 分页大小
     * @return java.util.List<com.chervon.iot.middle.api.vo.device.DeviceTopologyVo>
     **/
    List<DeviceTopologyVo> listDeviceTopology(String productKey, String deviceId, String topologyDeviceId,
                                              Date startTime, Date endTime, Integer type, long offset,
                                              Integer size);

    /**
     * 分页获取
     * @param offset:
     * @param pageSize:
     * @param logPageDto:
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.chervon.iot.middle.domain.dto.DeviceLogDto>
     **/
    List<DeviceLogDto> pageDeviceLogs(String productKey, Integer offset, Integer pageSize, LogPageDto logPageDto);

    /**
     * @param logPageDto:
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.chervon.iot.middle.domain.dto.DeviceLogDto>
     **/
    List<DeviceLogDto> getDeviceLogs(String productKey, LogPageDto logPageDto);

    /**
     * 获取设备列表(不分页)
     *
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.chervon.iot.middle.domain.dto.DeviceLogDto>
     **/
    List<DeviceLogDto> getDeviceLogList(String productKey, DeviceLogParamDto deviceLogParamDto);



    Long countDeviceLogs(String productKey, LogPageDto logPageDto);

    /**
     * 获取挂载的电池包
     * @param productKey 产品id
     * @param deviceId 设备id
     * @return 电池包id
     */
    String getMountedBattery(String productKey, String deviceId);

    /**
     * 时序数据库表增加字段
     * @param productKey 产品id
     * @param identifier 字段名
     * @param idType 字段类型
     */
    void addTdIdentifier(String productKey, String identifier, String idType);
}
