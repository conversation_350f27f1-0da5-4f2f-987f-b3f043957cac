package com.chervon.iot.middle.utils;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class DataSourceUtils {

    /**
     * 在指定数据源下执行无返回值操作
     *
     * @param dataSource 数据源名称
     * @param operation  操作
     */
    public static void executeWithDataSource(String dataSource, Runnable operation) {
        String originalDataSource = DynamicDataSourceContextHolder.peek();
        log.info("数据源切换: {} -> {}", originalDataSource, dataSource);

        DynamicDataSourceContextHolder.push(dataSource);
        try {
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.info("当前数据源: {}", currentDataSource);
            operation.run();
        } finally {
            DynamicDataSourceContextHolder.poll();
            String restoredDataSource = DynamicDataSourceContextHolder.peek();
            log.info("数据源恢复: {}", restoredDataSource);
        }
    }
}

