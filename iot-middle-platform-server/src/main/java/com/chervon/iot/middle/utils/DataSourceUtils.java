package com.chervon.iot.middle.utils;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class DataSourceUtils {
    /**
     * 在指定数据源下执行操作
     *
     * @param dataSource 数据源名称
     * @param operation  操作
     * @param <T>        返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithDataSource(String dataSource, Supplier<T> operation) {
        DynamicDataSourceContextHolder.push(dataSource);
        try {
            return operation.get();
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    /**
     * 在指定数据源下执行无返回值操作
     *
     * @param dataSource 数据源名称
     * @param operation  操作
     */
    public static void executeWithDataSource(String dataSource, Runnable operation) {
        DynamicDataSourceContextHolder.push(dataSource);
        try {
            operation.run();
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }
}

