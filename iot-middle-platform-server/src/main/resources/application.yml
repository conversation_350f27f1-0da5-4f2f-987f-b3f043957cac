# Tomcat
server:
  port: 7001

# Spring
spring:
  application:
    # 应用名称
    name: iot-middle-platform
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: sit

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      # server-addr: ${NACOS_SERVER_ADDR}
      server-addr: 127.0.0.1:8848
      discovery:
        # 注册组
        group: IOT_GROUP
        namespace: iot
      config:
        # 配置组
        group: IOT_GROUP
        namespace: iot
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
