<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.iot.middle.mapper.IotZTRUsageMapper">

    <select id="getDayUsageData" resultType="com.chervon.iot.middle.domain.pojo.CropperUsage">
        select device_id,sum(cast(idf_1035 as INT)) cuttingArea,sum(cast(idf_1031 as INT)) cuttingTime,
               max(cast(idf_5001 as INT)) maxSpeed,sum(cast(idf_5008 as INT)) drivingDistance
        from sub_product_${productKey}_${query.deviceId}
        where  idf_1024 <![CDATA[>]]> #{query.timeStart}
        and idf_1024 <![CDATA[<=]]> #{query.timeEnd}  group by device_id
    </select>

    <select id="getCoordinateHistory" resultType="com.chervon.iot.middle.domain.pojo.CoordinateHistory">
        select cast(idf_1024 as BIGINT) as ts1024,idf_1036 coordinate,cast(idf_5004 as INT) as status
        from sub_product_${productKey}_${query.deviceId}
        where  idf_1036 is not null and idf_5004 is not null
          and idf_1024 <![CDATA[>=]]> #{query.timeStart}
          and idf_1024 <![CDATA[<=]]> #{query.timeEnd} order by idf_1024
    </select>

    <select id="getDayWorkTimeHistory" resultType="com.chervon.iot.middle.api.pojo.usage.UsageKeyValue">
        select timetruncate(cast(idf_1024 as BIGINT), 1d) dataKey, sum(cast(idf_1031 as BIGINT)) dataValue
        from sub_product_${productKey}_${query.deviceId}
        where idf_1031 > '0'
          and idf_1024 <![CDATA[>]]> '${query.timeStart}'
          and idf_1024 <![CDATA[<=]]> '${query.timeEnd}'
        group by timetruncate(cast(idf_1024 as BIGINT), 1d)
    </select>

    <select id="getDayCuttingAreaHistory" resultType="com.chervon.iot.middle.api.pojo.usage.UsageKeyValue">
        select timetruncate(cast(idf_1024 as BIGINT),1d) dataKey,sum(cast(idf_1035 as BIGINT)) dataValue
        from sub_product_${productKey}_${query.deviceId}
        where idf_1035 > '0'
          and idf_1024 <![CDATA[>]]> '${query.timeStart}'
          and idf_1024 <![CDATA[<=]]> '${query.timeEnd}'
        group by timetruncate(cast(idf_1024 as BIGINT),1d)
    </select>


    <select id="getSumWorkTime" resultType="java.lang.Long">
        select sum(cast(idf_1031 as BIGINT)) dataValue
        from sub_product_${productKey}_${query.deviceId}
        where idf_1031 > '0'
          and idf_1024 <![CDATA[>=]]> '${query.timeStart}'
          and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
    </select>

    <select id="getSumCuttingArea" resultType="java.lang.Long">
        select sum(cast(idf_1035 as BIGINT)) dataValue
        from sub_product_${productKey}_${query.deviceId}
        where idf_1035 > '0'
          and idf_1024 <![CDATA[>=]]> '${query.timeStart}'
          and idf_1024 <![CDATA[<]]> '${query.timeEnd}'
    </select>


</mapper>
