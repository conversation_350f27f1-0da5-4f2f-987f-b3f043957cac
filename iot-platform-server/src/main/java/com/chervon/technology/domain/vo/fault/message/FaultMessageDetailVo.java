package com.chervon.technology.domain.vo.fault.message;

import com.chervon.technology.api.enums.PushMethodEnum;
import com.chervon.technology.domain.dto.TriggerDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/9/16 9:35
 */
@Data
public class FaultMessageDetailVo implements Serializable {
    /**
     * 消息ID
     */
    @ApiModelProperty("消息ID")
    private Long id;
    /**
     * 消息模板Id
     */
    @ApiModelProperty("消息模板Id")
    private Long messageTemplateId;
    /**
     * 处理建议Id
     */
    @ApiModelProperty("处理建议Id")
    private Long suggestionId;
    /**
     * 触发器列表
     */
    @ApiModelProperty("触发器列表")
    private List<TriggerDto> triggerDtoList;
    /**
     * 推送方式列表:
     * TOMBSTONE      墓碑
     * POPUP          APP弹窗
     * BANNER         APP BANNER
     * MESSAGE_MANAGE APP消息管理
     * MESSAGE        短信
     * MAIL           邮箱
     */
    @ApiModelProperty("推送方式列表")
    private List<PushMethodEnum> pushMethodList;
    /**
     * 推送类型：0告警后推送 1设备触发
     */
    @ApiModelProperty("推送类型：0告警后推送 1设备触发")
    private Integer pushType;
    /**
     * 累计触发多少秒内
     * 推送类型为1有效
     */
    @ApiModelProperty("累计触发多少秒内")
    private Integer countedTriggerTime;
    /**
     * 累计触发多少次
     * 推送类型为1有效
     */
    @ApiModelProperty("累计触发多少次")
    private Integer countedTriggerTimes;
    /**
     * 累计触发后，触发第几次： 0 第一次 1 最后一次
     * 推送类型为1有效
     */
    @ApiModelProperty("累计触发后，触发第几次： 0 第一次 1 最后一次")
    private Integer countedTriggerHandleTime;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType;

    @ApiModelProperty("推送的目标用户类型")
    private List<Integer> pushUserTypes;

}
