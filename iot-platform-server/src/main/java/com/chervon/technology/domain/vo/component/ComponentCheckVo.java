package com.chervon.technology.domain.vo.component;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-08-03
 */
@Data
public class ComponentCheckVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总成零件名称")
    private String componentName;

    @ApiModelProperty("总成零件类型，mcu：MCU，subDevice：子设备，bleModule：蓝牙模组，4gModule：4G")
    private String componentType;
}
