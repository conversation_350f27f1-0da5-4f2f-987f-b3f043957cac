package com.chervon.technology.domain.dto.rule;

import com.chervon.technology.domain.enums.RuleCondition;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @className IotUpdateOnlineStatusDto
 * @description
 * @date 2022/3/23 16:16
 */
@Data
public class RuleEngineDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物模型属性id
     **/
    private String propertyId;

    /**
     * 判断规则
     **/
    private RuleCondition ruleCondition;

    /**
     * 条件值
     **/
    private String value;
}
