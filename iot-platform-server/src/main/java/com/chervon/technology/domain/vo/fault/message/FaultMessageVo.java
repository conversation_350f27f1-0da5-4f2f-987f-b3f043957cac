package com.chervon.technology.domain.vo.fault.message;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.technology.api.enums.PushMethodEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
public class FaultMessageVo implements Serializable {
    /**
     * 消息ID
     */
    @ApiModelProperty("消息ID")
    private Long id;
    /**
     * 消息标题
     */
    @ApiModelProperty("消息标题")
    private String title;
    /**
     * 消息内容
     */
    @ApiModelProperty("消息内容")
    private String content;
    /**
     * 消息展示类型： 1 text，2 voice
     */
    private Integer messageDisplayType;
    /**
     * 推送方式
     */
    @ApiModelProperty("推送方式")
    private List<PushMethodEnum> pushMethod;
    /**
     * 状态
     */
    @ApiModelProperty("状态：0未启用 1启用")
    private Long status;
    @ApiModelProperty("是否规则引擎同步数据：0否 1是")
    private Integer isSync;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
    /**
     * 消息模板Id
     */
    @ApiModelProperty("消息模板Id")
    private Long messageTemplateId;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long productId;

    /**
     * 推送成功条数
     */
    @ApiModelProperty("推送成功条数")
    private Integer pushSuccessNum;

    /**
     * 推送失败条数
     */
    @ApiModelProperty("推送失败条数")
    private Integer pushFailNum;

    @ApiModelProperty("适用app类型 1 ego 2 fleet")
    private List<Integer> businessType;

    @ApiModelProperty(hidden = true)
    private String businessTypeStr;
}
