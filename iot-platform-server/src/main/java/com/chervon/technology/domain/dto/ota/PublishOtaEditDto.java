package com.chervon.technology.domain.dto.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className PublishOtaDto
 * @description 发布固件
 * @date 2022/7/13 10:58
 */
@ApiModel("发布固件")
@Data
public class PublishOtaEditDto {

    /**
     * 任务id
     **/
    @ApiModelProperty("任务id")
    private String jobId;

    /**
     * 固件包列表
     **/
    @ApiModelProperty("固件包列表")
    private List<PackageDto> packages;
}
