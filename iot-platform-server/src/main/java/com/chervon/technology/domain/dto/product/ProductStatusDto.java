package com.chervon.technology.domain.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-07-06
 */
@Data
public class ProductStatusDto implements Serializable {

    /**
     * 产品Id
     */
    @NotNull
    @ApiModelProperty("产品PID")
    private Long productId;

    /**
     * 操作：0申请封板，1确认封板，2拒绝封板，3取消封板，4申请解版，5确认解版，6拒绝解版，7取消封板
     */
    @NotNull
    @ApiModelProperty("操作：0申请封板，1确认封板，2拒绝封板，3取消封板，4申请解版，5确认解版，6拒绝解版，7取消封板")
    private Integer operationType;

    @ApiModelProperty("审批原因")
    private String description;
}
