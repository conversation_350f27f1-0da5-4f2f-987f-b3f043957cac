package com.chervon.technology.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品信息表
 *
 * <AUTHOR>
 * @date 2022-04-25 19:00:12
 */
@Data
@TableName("product")
@EqualsAndHashCode(callSuper = true)
public class Product extends BaseDo {

    private static final long serialVersionUID = -4429223629030974440L;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称多语言Id")
    private String productName;
    /**
     * 产品型号
     */
    private String model;

    /**
     * 商品型号Model#
     */
    @ApiModelProperty("商品型号Model#")
    private String commodityModel;

    @ApiModelProperty("产品SNCode")
    private String productSnCode;
    /**
     * 产品图标
     */
    private String productIcon;

    /**
     * 品类Id
     */
    private Long categoryId;
    /**
     * 品牌Id
     */
    private Long brandId;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，
     * notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    private String productType;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品发布状态
     * 无状态 -
     * 待发布 to_be_release
     * 发布审核 release_approve
     * 已发布 PReleased

     * 已下架 POffReleased
     */
    private String releaseStatus;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String secretKey;

    /**
     * 运营平台的备注
     */
    private String operationRemark;

    /**
     * 创建类型:0 技术平台创建，1运营平台创建
     */
    private Integer createType;

    /**
     * 问卷模板：commonTemplate， extendedWarrantyTemplate，用逗号间隔
     */
    private String questionTemplate;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;

    @TableField(exist = false)
    private String flag;

    @ApiModelProperty("适用app类型 1 ego 2 fleet，多个用逗号分割")
    private String businessType;

    @ApiModelProperty("是否在app展示 0：不展示 1：展示 ,默认 0")
    private Integer appShowStatus;

    @ApiModelProperty("app展示序号")
    private Integer appShowOrder;


    /**
     * 是否支持分享, false:不支持, true:支持
     */
    private Boolean isSharingSupported;
    /**
     * 分享权限, 1:账户权限最高，2：设备权限最高
     */
    private Integer shareAuthorityType;
}
