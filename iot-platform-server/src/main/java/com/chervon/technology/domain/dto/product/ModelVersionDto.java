package com.chervon.technology.domain.dto.product;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 根据物模型产品snCode和版本号获取物模型
 */
@Data
public class ModelVersionDto implements Serializable {

    /**
     * 产品SNCOde
     */
    @NotEmpty
    private String productSnCode;

    /**
     * 产品version
     */
    @NotNull
    private Long version;
}
