package com.chervon.technology.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.technology.api.RemoteSmsChargingService;
import com.chervon.technology.api.core.BaseRemoteReqDto;
import com.chervon.technology.api.dto.charging.SmsChargingDetailPageDto;
import com.chervon.technology.api.dto.charging.SmsChargingPageDto;
import com.chervon.technology.api.vo.charging.SmsChargingDetailExcel;
import com.chervon.technology.api.vo.charging.SmsChargingDetailVo;
import com.chervon.technology.api.vo.charging.SmsChargingExcel;
import com.chervon.technology.api.vo.charging.SmsChargingVo;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.domain.dataobject.Charging;
import com.chervon.technology.domain.dataobject.SmsCharging;
import com.chervon.technology.mapper.ChargingMapper;
import com.chervon.technology.mapper.SmsChargingMapper;
import com.chervon.technology.service.DictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/27 17:47
 */
@DubboService
@Service
@Slf4j
public class RemoteSmsChargingServiceImpl implements RemoteSmsChargingService {

    @Resource
    private ChargingMapper chargingMapper;

    @Resource
    private SmsChargingMapper smsChargingMapper;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private DictService dictService;

    private List<Long> findMsgTitleLangIds(String title) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(title)) {
            return res;
        }
        List<Charging> list = chargingMapper.selectList(new LambdaQueryWrapper<Charging>().eq(Charging::getType, "sms"));
        List<Long> titleLangIds = list.stream().map(Charging::getMsgTitleLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(titleLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(title, titleLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(title).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<SmsChargingVo> smsChargingPage(BaseRemoteReqDto<SmsChargingPageDto> req) {
        if (req == null || req.getReq() == null) {
            return null;
        }
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        List<Long> msgTitleLangIds = findMsgTitleLangIds(req.getReq().getMsgTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getReq().getMsgTitle()) && CollectionUtils.isEmpty(msgTitleLangIds)) {
            return new PageResult<>(req.getReq().getPageNum(), req.getReq().getPageSize(), 0);
        }
        IPage<SmsChargingVo> page = chargingMapper.smsChargingPage(new Page<>(req.getReq().getPageNum(), req.getReq().getPageSize()), req.getReq(), msgTitleLangIds);
        List<Long> chargingIds = page.getRecords().stream().map(SmsChargingVo::getChargingId).collect(Collectors.toList());
        List<SmsCharging> smsChargingList = new ArrayList<>();
        if (!chargingIds.isEmpty()) {
            smsChargingList = smsChargingMapper.selectList(new LambdaQueryWrapper<SmsCharging>()
                    .in(SmsCharging::getChargingId, chargingIds)
                    .ge(StringUtils.isNotBlank(req.getReq().getChargingStartTime()), SmsCharging::getPushTime, req.getReq().getChargingStartTime())
                    .le(StringUtils.isNotBlank(req.getReq().getChargingEndTime()), SmsCharging::getPushTime, req.getReq().getChargingEndTime()));
        }
        Map<Long, List<SmsCharging>> group = smsChargingList.stream().collect(Collectors.groupingBy(SmsCharging::getChargingId));
        PageResult<SmsChargingVo> res = new PageResult<>(req.getReq().getPageNum(), req.getReq().getPageSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            SmsChargingVo vo = new SmsChargingVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMsgTitle(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getMsgTitle(), LocaleContextHolder.getLocale().getLanguage()));
            List<SmsCharging> list = group.getOrDefault(e.getChargingId(), new ArrayList<>());
            SmsCharging max = list.stream().max(Comparator.comparing(SmsCharging::getPushTime)).orElse(new SmsCharging());
            vo.setChargingEndTime(max.getPushTime());
            SmsCharging min = list.stream().min(Comparator.comparing(SmsCharging::getPushTime)).orElse(new SmsCharging());
            vo.setChargingStartTime(min.getPushTime());
            vo.setTotalSend(list.size());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public List<SmsChargingExcel> smsChargingList(BaseRemoteReqDto<SmsChargingPageDto> req) {
        if (req == null || req.getReq() == null) {
            return null;
        }
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        List<Long> msgTitleLangIds = findMsgTitleLangIds(req.getReq().getMsgTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getReq().getMsgTitle()) && CollectionUtils.isEmpty(msgTitleLangIds)) {
            return new ArrayList<>();
        }
        List<SmsChargingVo> data = chargingMapper.smsChargingList(req.getReq(), msgTitleLangIds);
        List<Long> chargingIds = data.stream().map(SmsChargingVo::getChargingId).collect(Collectors.toList());
        List<SmsCharging> smsChargingList = new ArrayList<>();
        if (!chargingIds.isEmpty()) {
            smsChargingList = smsChargingMapper.selectList(new LambdaQueryWrapper<SmsCharging>()
                    .in(SmsCharging::getChargingId, chargingIds)
                    .ge(StringUtils.isNotBlank(req.getReq().getChargingStartTime()), SmsCharging::getPushTime, req.getReq().getChargingStartTime())
                    .le(StringUtils.isNotBlank(req.getReq().getChargingEndTime()), SmsCharging::getPushTime, req.getReq().getChargingEndTime()));
        }
        Map<Long, List<SmsCharging>> group = smsChargingList.stream().collect(Collectors.groupingBy(SmsCharging::getChargingId));
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList("messageType"));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return data.stream().map(e -> {
            SmsChargingExcel excel = new SmsChargingExcel();
            excel.setChargingId(e.getChargingId());
            excel.setMsgId(e.getMsgId());
            excel.setMsgTitle(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getMsgTitle(), LocaleContextHolder.getLocale().getLanguage()));
            List<SmsCharging> list = group.getOrDefault(e.getChargingId(), new ArrayList<>());
            SmsCharging max = list.stream().max(Comparator.comparing(SmsCharging::getPushTime)).orElse(new SmsCharging());
            excel.setChargingEndTime(DateTimeZoneUtil.format(max.getPushTime(), req.getReq().getZone()));
            SmsCharging min = list.stream().min(Comparator.comparing(SmsCharging::getPushTime)).orElse(new SmsCharging());
            excel.setChargingStartTime(DateTimeZoneUtil.format(min.getPushTime(), req.getReq().getZone()));
            excel.setMsgType(collect.get("messageType").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getMsgType()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setCommodityModel(e.getCommodityModel());
            excel.setTotalSend(list.size());
            excel.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getReq().getZone()));
            return excel;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<SmsChargingDetailVo> smsChargingDetailPage(BaseRemoteReqDto<SmsChargingDetailPageDto> req) {
        if (req == null || req.getReq() == null) {
            return null;
        }
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        List<Long> msgTitleLangIds = findMsgTitleLangIds(req.getReq().getMsgTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getReq().getMsgTitle()) && CollectionUtils.isEmpty(msgTitleLangIds)) {
            return new PageResult<>(req.getReq().getPageNum(), req.getReq().getPageSize(), 0);
        }
        IPage<SmsChargingDetailVo> page = smsChargingMapper.smsChargingDetailPage(new Page<>(req.getReq().getPageNum(), req.getReq().getPageSize()), req.getReq(), msgTitleLangIds);
        PageResult<SmsChargingDetailVo> res = new PageResult<>(req.getReq().getPageNum(), req.getReq().getPageSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            SmsChargingDetailVo vo = new SmsChargingDetailVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMsgTitle(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getMsgTitle(), LocaleContextHolder.getLocale().getLanguage()));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Override
    public List<SmsChargingDetailExcel> smsChargingDetailList(BaseRemoteReqDto<SmsChargingDetailPageDto> req) {
        if (req == null || req.getReq() == null) {
            return null;
        }
        LocaleContextHolder.setLocale(new Locale(req.getLanguage()));
        List<Long> msgTitleLangIds = findMsgTitleLangIds(req.getReq().getMsgTitle());
        // 如果没有匹配的多语言id，则返回空
        if (StringUtils.isNotBlank(req.getReq().getMsgTitle()) && CollectionUtils.isEmpty(msgTitleLangIds)) {
            return new ArrayList<>();
        }
        List<SmsChargingDetailVo> data = smsChargingMapper.smsChargingDetailList(req.getReq(), msgTitleLangIds);
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList("messageType", "pushMethod"));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return data.stream().map(e -> {
            SmsChargingDetailExcel excel = new SmsChargingDetailExcel();
            excel.setMsgRecordId(e.getMsgRecordId());
            excel.setMsgType(collect.get("messageType").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getMsgType()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setMsgTitle(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getMsgTitle(), LocaleContextHolder.getLocale().getLanguage()));
            excel.setMsgContent(e.getMsgContent());
            excel.setPushType(collect.get("pushMethod").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getPushType()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setToUserId(e.getToUserId());
            excel.setToUserPhoneNumber(e.getToUserPhoneNumber());
            excel.setToUserRegion(e.getToUserRegion());
            excel.setPushTime(DateTimeZoneUtil.format(e.getPushTime(), req.getReq().getZone()));
            return excel;
        }).collect(Collectors.toList());
    }
}
