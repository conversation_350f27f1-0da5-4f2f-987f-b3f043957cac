package com.chervon.technology.controller;

import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.iot.middle.api.pojo.thingmodel.IotThingModel;
import com.chervon.technology.domain.dto.product.model.*;
import com.chervon.technology.domain.vo.product.ModelEventVo;
import com.chervon.technology.domain.vo.product.ModelPropertyVo;
import com.chervon.technology.domain.vo.product.ModelServiceVo;
import com.chervon.technology.service.ProductModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/product/model")
@Api(tags = "物模型相关接口")
public class ProductModelController {

    @Autowired
    private ProductModelService productModelService;
    /**
     * 添加单个属性定义
     *
     * @param propertyAdd 属性信息
     * @return 添加结果
     */
    @ApiOperation("添加单个属性定义")
    @RequestMapping(value = "/property/add", method = RequestMethod.POST)
//    @Log(businessType = BusinessType.VIEW)
    public R<Boolean> addProperty(@Validated @RequestBody PropertyAddDto propertyAdd) {
        return R.ok(productModelService.addProperty(propertyAdd));
    }

    /**
     * 编辑单个属性定义
     *
     * @param propertyEdit 属性信息
     * @return 添加结果
     */
    @ApiOperation("编辑单个属性定义")
    @RequestMapping(value = "/property/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public R<Boolean> editProperty(@Validated @RequestBody PropertyEditDto propertyEdit) {
        return R.ok(productModelService.editProperty(propertyEdit));
    }

    /**
     * 删除
     *
     * @param propertyDelete 删除
     * @return 删除
     */
    @ApiOperation("删除单个属性定义")
    @RequestMapping(value = "/property/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public R<Boolean> deleteProperty(@Validated @RequestBody ModelDeleteDto propertyDelete) {
        return R.ok(productModelService.deleteProperty(propertyDelete));
    }


    /**
     * 获取物模型属性列表
     * @param search
     * @return
     */
    @ApiOperation("分页获取属性定义")
    @RequestMapping(value = "/property/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<ModelPropertyVo>> getProperties(@Validated @RequestBody SearchModelDto search) {
        return R.ok(productModelService.getProperties(search));
    }


    /**
     * 添加单个事件定义
     *
     * @param event 事件信息
     * @return 结果
     */
    @ApiOperation("添加单个事件定义")
    @RequestMapping(value = "/event/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public R<Boolean> addEvent(@Validated @RequestBody EventAddDto event) {
        return R.ok(productModelService.addEvent(event));
    }

    /**
     * 编辑单个事件定义
     *
     * @param event 编辑信息
     * @return 结果
     */
    @ApiOperation("编辑单个事件定义")
    @RequestMapping(value = "/event/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public R<Boolean> editEvent(@Validated @RequestBody EventEditDto event) {
        return R.ok(productModelService.editEvent(event));
    }

    /**
     * 删除单个事件定义
     *
     * @param event 编辑信息
     * @return 结果
     */
    @ApiOperation("删除单个事件定义")
    @RequestMapping(value = "/event/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public R<Boolean> deleteEvent(@Validated @RequestBody ModelDeleteDto event) {
        return R.ok(productModelService.deleteEvent(event));
    }


    /**
     * 分页获取物模型事件列表
     *
     * @param search
     * @return
     */
    @ApiOperation("分页获取物模型事件列表")
    @RequestMapping(value = "/event/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<ModelEventVo>> getModelEvents(@Validated @RequestBody SearchModelDto search) {
        return R.ok(productModelService.getModelEvents(search));
    }

    /**
     * 添加功能定义
     *
     * @param serviceAdd 功能定义信息
     * @return
     */
    @ApiOperation("添加功能定义")
    @RequestMapping(value = "/service/add", method = RequestMethod.POST)
    @Log(businessType = BusinessType.INSERT)
    public R<Boolean> addService(@Validated @RequestBody ServiceAddDto serviceAdd) {
        return R.ok(productModelService.addService(serviceAdd));
    }

    /**
     * 编辑功能定义
     *
     * @param service 功能定义信息
     * @return
     */
    @ApiOperation("编辑功能定义")
    @RequestMapping(value = "/service/edit", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EDIT)
    public R<Boolean> editService(@Validated @RequestBody ServiceEditDto service) {
        return R.ok(productModelService.editService(service));
    }

    /**
     * 删除功能定义
     *
     * @param service 功能定义信息
     * @return
     */
    @ApiOperation("删除功能定义")
    @RequestMapping(value = "/service/delete", method = RequestMethod.POST)
    @Log(businessType = BusinessType.DELETE)
    public R<Boolean> deleteService(@Validated @RequestBody ModelDeleteDto service) {
        return R.ok(productModelService.deleteService(service));
    }

    /**
     * 分页获取物模型服务列表
     *
     * @param search
     * @return
     */
    @ApiOperation("分页获取物模型服务列表")
    @RequestMapping(value = "/service/list", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<PageResult<ModelServiceVo>> getModelServices(@Validated @RequestBody SearchModelDto search) {
        return R.ok(productModelService.getModelServices(search));
    }

    /**
     * 物模型文件导入
     *
     * @return
     */
    @ApiOperation("物模型文件导入")
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @Log(businessType = BusinessType.IMPORT)
    public R<Boolean> importModel(@RequestParam(value = "productId") Long productId,
                                  @RequestParam(value = "file") MultipartFile file) {
        return R.ok(productModelService.importModel(productId, file));
    }

    /**
     * 查看物模型
     *
     * @return
     */
    @ApiOperation("查看物模型，参数为产品id")
    @RequestMapping(value = "/look", method = RequestMethod.POST)
    @Log(businessType = BusinessType.VIEW)
    public R<IotThingModel> lookModel(@Validated @RequestBody SingleInfoReq<Long> req) {
        return R.ok(productModelService.getModel(req.getReq()));
    }


    /**
     * 下载物模型
     *
     * @param req 产品model
     */
    @ApiOperation("下载物模型，参数为产品Model")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @Log(businessType = BusinessType.EXPORT)
    public R<IotThingModel> exportModel(@Validated @RequestBody SingleInfoReq<Long> req) {
        return R.ok(productModelService.exportModel(req.getReq()));
    }

}
