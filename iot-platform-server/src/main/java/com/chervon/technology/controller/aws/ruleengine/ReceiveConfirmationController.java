package com.chervon.technology.controller.aws.ruleengine;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "AWS规则引擎接收-创建AWS规则引擎接收")
@RestController
@RequestMapping("/rule")
@Slf4j
public class ReceiveConfirmationController {

    /**
     *
     * 创建aws规则引擎时，aws触发发送的目标地址确认token 接收接口
     *
     * @param confirmationToken: 目标地址确认token
     * <AUTHOR>
     * @date 16:53 2022/5/16
     * @return: void
     **/
    @PostMapping
    @ApiOperation("接收规则引擎目标地址确认token")
    public void receiveConfirmationToken(@RequestParam String confirmationToken) {
        log.info("confirmationToken:{}", confirmationToken);
    }
}
