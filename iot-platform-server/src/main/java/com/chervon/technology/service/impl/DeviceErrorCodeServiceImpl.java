package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.DeviceErrorCodeExcel;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.entity.BaseType;
import com.chervon.technology.entity.BaseTypeModel;
import com.chervon.technology.entity.DeviceErrorCode;
import com.chervon.technology.mapper.DeviceErrorCodeMapper;
import com.chervon.technology.req.DeviceErrorCodeDto;
import com.chervon.technology.req.DeviceErrorCodePageDto;
import com.chervon.technology.req.ThingModelPageDto;
import com.chervon.technology.resp.DeviceErrorCodePageVo;
import com.chervon.technology.resp.ThingModelPageVo;
import com.chervon.technology.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/7 13:42
 */
@Service
@Slf4j
public class DeviceErrorCodeServiceImpl extends ServiceImpl<DeviceErrorCodeMapper, DeviceErrorCode> implements DeviceErrorCodeService {

    @DubboReference
    private RemoteCategoryService remoteCategoryService;

    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @Autowired
    private DictService dictService;

    @Autowired
    private BaseTypeService baseTypeService;

    @Autowired
    private BaseTypeModelService baseTypeModelService;

    @Autowired
    private ProductModelService productModelService;

    private static final String THING_MODEL_EVENT_TYPE_DIST = "thingModelEventType";

    private List<Long> findModelNameLangIds(String modelName) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(modelName)) {
            return res;
        }
        List<DeviceErrorCode> list = this.list();
        List<Long> modelNameLangIds = list.stream().map(DeviceErrorCode::getModelLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(modelNameLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(modelName, modelNameLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(modelName).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    private List<Long> findFunctionNameLangIds(String functionName) {
        List<Long> res = new ArrayList<>();
        if (StringUtils.isBlank(functionName)) {
            return res;
        }
        List<DeviceErrorCode> list = this.list();
        List<Long> functionNameLangIds = list.stream().map(DeviceErrorCode::getFunctionLangId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(functionNameLangIds)) {
            return res;
        }
        Map<@NotBlank String, @NotNull List<MultiLanguageBo>> map = remoteMultiLanguageService.listByTextLike(new HashMap<String, List<Long>>() {{
            put(functionName, functionNameLangIds);
        }}, LocaleContextHolder.getLocale().getLanguage());
        return map.get(functionName).stream().map(MultiLanguageBo::getLangId).collect(Collectors.toList());
    }

    @Override
    public PageResult<DeviceErrorCodePageVo> page(DeviceErrorCodePageDto req) {
        List<Long> modelNameLangIds = findModelNameLangIds(req.getModelName());
        List<Long> functionNameLangIds = findFunctionNameLangIds(req.getFunctionName());
        // 如果没有匹配的多语言id，则返回空
        if ((StringUtils.isNotBlank(req.getModelName()) && CollectionUtils.isEmpty(modelNameLangIds))
                || (StringUtils.isNotBlank(req.getFunctionName()) && CollectionUtils.isEmpty(functionNameLangIds))) {
            return new PageResult<>(req.getPageNum(), req.getPageSize(), 0);
        }
        LambdaQueryWrapper<DeviceErrorCode> wrapper = new LambdaQueryWrapper<DeviceErrorCode>()
                .like(StringUtils.isNotBlank(req.getDeviceErrorCodeId()), DeviceErrorCode::getId, req.getDeviceErrorCodeId())
                .eq(req.getCategoryId() != null, DeviceErrorCode::getCategoryId, req.getCategoryId())
                .like(StringUtils.isNotBlank(req.getCategoryCode()), DeviceErrorCode::getCategoryCode, req.getCategoryCode())
                .like(StringUtils.isNotBlank(req.getModelCode()), DeviceErrorCode::getModelCode, req.getModelCode())
                .like(StringUtils.isNotBlank(req.getProductModel()), DeviceErrorCode::getProductModel, req.getProductModel())
                .eq(StringUtils.isNotBlank(req.getEventCode()), DeviceErrorCode::getEventCode, req.getEventCode())
                .like(StringUtils.isNotBlank(req.getErrorCode()), DeviceErrorCode::getErrorCode, req.getErrorCode())
                .in(!CollectionUtils.isEmpty(modelNameLangIds), DeviceErrorCode::getModelLangId, modelNameLangIds)
                .in(!CollectionUtils.isEmpty(functionNameLangIds), DeviceErrorCode::getFunctionLangId, functionNameLangIds)
                .ge(StringUtils.isNotBlank(req.getCreateStartTime()), DeviceErrorCode::getCreateTime, req.getCreateStartTime())
                .le(StringUtils.isNotBlank(req.getCreateEndTime()), DeviceErrorCode::getCreateTime, req.getCreateEndTime())
                .ge(StringUtils.isNotBlank(req.getUpdateStartTime()), DeviceErrorCode::getUpdateTime, req.getUpdateStartTime())
                .le(StringUtils.isNotBlank(req.getUpdateEndTime()), DeviceErrorCode::getUpdateTime, req.getUpdateEndTime())
                .orderByDesc(DeviceErrorCode::getCreateTime);
        Page<DeviceErrorCode> page = this.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);
        PageResult<DeviceErrorCodePageVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            DeviceErrorCodePageVo vo = new DeviceErrorCodePageVo();
            BeanUtils.copyProperties(e, vo);
            vo.setDeviceErrorCodeId(e.getId());
            if (StringUtils.isNotBlank(e.getCategoryLangCode())) {
                vo.setCategoryName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getCategoryLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(e.getModelLangCode())) {
                vo.setModelName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getModelLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(e.getFunctionLangCode())) {
                vo.setFunction(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getFunctionLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(e.getRemarkLangCode())) {
                vo.setRemark(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getRemarkLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(List<DeviceErrorCodeDto> reqs) {
        Map<String, DeviceErrorCodeDto> collect = reqs.stream().collect(Collectors.toMap(e -> e.getProductId() + "##" + e.getProductModel() + "##" + e.getEventCode() + "##" + e.getFunctionLangId() + "##" + e.getErrorCode() + "##" + e.getRemarkLangId(), Function.identity(), (e1, e2) -> e2));
        List<DeviceErrorCode> list = new ArrayList<>();
        collect.forEach((k, v) -> {
            // 校验错误码是否可以出品类和模块
            Map<String, Object> map = parseErrorCode(v.getErrorCode());
            // 校验错误码是否已存在
            checkErrorCodeExist(k);
            BaseType baseType = (BaseType) map.get("baseType");
            BaseTypeModel baseTypeModel = (BaseTypeModel) map.get("baseTypeModel");
            DeviceErrorCode one = new DeviceErrorCode();

            one.setBaseTypeId(baseType.getId());
            one.setCategoryId(baseType.getCategoryId());
            one.setCategoryLangId(baseType.getCategoryLangId());
            one.setCategoryLangCode(baseType.getCategoryLangCode());
            one.setCategoryCode(baseType.getCode());

            one.setModelId(baseTypeModel.getId());
            one.setModelLangId(baseTypeModel.getNameLangId());
            one.setModelLangCode(baseTypeModel.getNameLangCode());
            one.setModelCode(baseTypeModel.getCode());

            one.setProductId(v.getProductId());
            one.setProductModel(v.getProductModel());
            one.setEventCode(v.getEventCode());
            one.setFunctionLangId(v.getFunctionLangId());
            one.setFunctionLangCode(v.getFunctionLangCode());
            one.setErrorCode(v.getErrorCode());
            one.setRemarkLangId(v.getRemarkLangId());
            one.setRemarkLangCode(v.getRemarkLangCode());

            list.add(one);
        });
        this.saveBatch(list);
    }

    private void checkErrorCodeExist(String key) {
        String[] split = key.split("##");
        String productId = split[0];
        String productModel = split[1];
        String eventCode = split[2];
        String functionLangId = split[3];
        String errorCode = split[4];
        String remarkLangId = split[5];
        List<DeviceErrorCode> list = this.list(new LambdaQueryWrapper<DeviceErrorCode>().eq(DeviceErrorCode::getProductId, productId)
                .eq(DeviceErrorCode::getProductModel, productModel)
                .eq(DeviceErrorCode::getEventCode, eventCode)
                .eq(DeviceErrorCode::getFunctionLangId, functionLangId)
                .eq(DeviceErrorCode::getErrorCode, errorCode)
                .eq(DeviceErrorCode::getRemarkLangId, remarkLangId));
        if (list.size() > 0) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_EXIST, "error_code", errorCode);
        }
    }

    private Map<String, Object> parseErrorCode(String errorCode) {
        Map<String, Object> res = new HashMap<>();
        if (errorCode.length() < 4) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_LENGTH_LG_4);
        }
        String categoryCode = errorCode.substring(0, 2);
        String modelCode = errorCode.substring(2, 4);
        BaseTypeModel model = baseTypeModelService.getByCode(categoryCode, modelCode);
        if (model == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_PARSE_ERROR1, modelCode);
        }
        res.put("baseTypeModel", model);
        BaseType type = baseTypeService.getById(model.getBaseTypeId());
        if (type == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ERROR_CODE_PARSE_ERROR2, model.getBaseTypeId());
        }
        res.put("baseType", type);
        return res;
    }

    @Override
    public List<DeviceErrorCodeExcel> listData(DeviceErrorCodePageDto req) {
        List<Long> modelNameLangIds = findModelNameLangIds(req.getModelName());
        List<Long> functionNameLangIds = findFunctionNameLangIds(req.getFunctionName());
        // 如果没有匹配的多语言id，则返回空
        if ((StringUtils.isNotBlank(req.getModelName()) && CollectionUtils.isEmpty(modelNameLangIds))
                || (StringUtils.isNotBlank(req.getFunctionName()) && CollectionUtils.isEmpty(functionNameLangIds))) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DeviceErrorCode> wrapper = new LambdaQueryWrapper<DeviceErrorCode>()
                .like(StringUtils.isNotBlank(req.getDeviceErrorCodeId()), DeviceErrorCode::getId, req.getDeviceErrorCodeId())
                .eq(req.getCategoryId() != null, DeviceErrorCode::getCategoryId, req.getCategoryId())
                .like(StringUtils.isNotBlank(req.getCategoryCode()), DeviceErrorCode::getCategoryCode, req.getCategoryCode())
                .like(StringUtils.isNotBlank(req.getModelCode()), DeviceErrorCode::getModelCode, req.getModelCode())
                .like(StringUtils.isNotBlank(req.getProductModel()), DeviceErrorCode::getProductModel, req.getProductModel())
                .eq(StringUtils.isNotBlank(req.getEventCode()), DeviceErrorCode::getEventCode, req.getEventCode())
                .like(StringUtils.isNotBlank(req.getErrorCode()), DeviceErrorCode::getErrorCode, req.getErrorCode())
                .in(!CollectionUtils.isEmpty(modelNameLangIds), DeviceErrorCode::getModelLangId, modelNameLangIds)
                .in(!CollectionUtils.isEmpty(functionNameLangIds), DeviceErrorCode::getFunctionLangId, functionNameLangIds)
                .orderByDesc(DeviceErrorCode::getCreateTime);
        List<DeviceErrorCode> list = this.list(wrapper);

        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(THING_MODEL_EVENT_TYPE_DIST));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        return list.stream().map(e -> {
            DeviceErrorCodeExcel vo = new DeviceErrorCodeExcel();
            BeanUtils.copyProperties(e, vo);
            vo.setDeviceErrorCodeId(e.getId() + "");
            if (StringUtils.isNotBlank(e.getCategoryLangCode())) {
                vo.setCategoryName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getCategoryLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(e.getModelLangCode())) {
                vo.setModelName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getModelLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            if (StringUtils.isNotBlank(e.getFunctionLangCode())) {
                vo.setFunction(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getFunctionLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            // 设置事件类型
            vo.setEvent(collect.get(THING_MODEL_EVENT_TYPE_DIST).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getEventCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            if (StringUtils.isNotBlank(e.getRemarkLangCode())) {
                vo.setRemark(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(e.getRemarkLangCode(), LocaleContextHolder.getLocale().getLanguage()));
            }
            vo.setCreateTime(DateTimeZoneUtil.format(e.getCreateTime(), req.getZone()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<ThingModelPageVo> thingModelPage(ThingModelPageDto req) {
        if (req.getProductId() == null) {
            return new PageResult<>(req.getPageNum(), req.getPageSize(), 0);
        }
        PageResult<ThingModelPageVo> res = productModelService.thingModelPage(req);
        List<String> collect = this.list().stream().map(e -> e.getProductId() + "##" + e.getProductModel() + "##" + e.getEventCode() + "##" + e.getFunctionLangId() + "##" + e.getErrorCode() + "##" + e.getRemarkLangId()).collect(Collectors.toList());
        res.getList().forEach(e -> e.setSelected(collect.contains(e.getProductId() + "##" + e.getProductModel() + "##" + e.getEventCode() + "##" + e.getFunctionLangId() + "##" + e.getErrorCode() + "##" + e.getRemarkLangId())));
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long deviceErrorCodeId) {
        DeviceErrorCode deviceErrorCode = this.getById(deviceErrorCodeId);
        if (deviceErrorCode != null) {
            // 清理多语言数据
            List<String> langCodes = new ArrayList<>();
            langCodes.add(deviceErrorCode.getCategoryLangCode());
            langCodes.add(deviceErrorCode.getFunctionLangCode());
            langCodes.add(deviceErrorCode.getRemarkLangCode());
            langCodes.add(deviceErrorCode.getRemarkLangCode());
            remoteMultiLanguageService.deleteByLangCodes(langCodes);
        }
        this.removeById(deviceErrorCodeId);
    }
}
