package com.chervon.technology.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.SearchName;
import com.chervon.technology.domain.dto.component.CheckComponentDto;
import com.chervon.technology.domain.dto.product.PidComponentAddDto;
import com.chervon.technology.domain.dto.product.ProductComponentDto;
import com.chervon.technology.domain.entity.Firmware;
import com.chervon.technology.domain.vo.component.ComponentCheckVo;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.domain.vo.product.ProductBaseInfoVo;
import com.chervon.technology.mapper.ComponentMapper;
import com.chervon.technology.mapper.ProductComponentMapper;
import com.chervon.technology.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-17
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProductComponentServiceImpl extends ServiceImpl<ProductComponentMapper, ProductComponent>
        implements ProductComponentService {

    private final ComponentMapper componentMapper;

    private final ComponentService componentService;


    private final ProductComponentMapper productComponentMapper;

    private final CommonService commonService;

    private final FirmwareService firmwareService;

    private final ProductService productService;

    @DubboReference(timeout = 12000, retries = 0)
    private RemoteCategoryService categoryService;

    @Override
    public ProductComponent getByPidAndComponent(Long pid, String componentCode) {
        LambdaQueryWrapper<ProductComponent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductComponent::getProductId, pid);
        wrapper.eq(ProductComponent::getComponentNo, componentCode);
        return this.getOne(wrapper);
    }

    @Override
    public void removeByPid(Long productId) {
        remove(Wrappers.<ProductComponent>lambdaQuery().eq(ProductComponent::getProductId, productId));
    }

    @Override
    public List<ProductComponent> getByComponents(Long pid, List<String> codes) {
        LambdaQueryWrapper<ProductComponent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductComponent::getProductId, pid);
        wrapper.in(ProductComponent::getComponentNo, codes);
        return this.list(wrapper);
    }

    @Override
    public void addComponents(PidComponentAddDto pidComponentAdd) {
        long pId = pidComponentAdd.getProductId();
        // 校验产品是否存在
        productService.checkExistAndGetProductById(pId);
        // 判断产品总成是否存在
        List<Component> components = componentService.getByNos(pidComponentAdd.getComponentNos());
        if (CollectionUtils.isEmpty(components) || components.size() < pidComponentAdd.getComponentNos().size()) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NOT_EXIST, pidComponentAdd.getComponentNos());
        }
        // 首先需要判断是否已经关联
        List<ProductComponent> list = this.getByComponents(pId,
                pidComponentAdd.getComponentNos());
        if (!CollectionUtils.isEmpty(list)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_COMPONENT_ALREADY_EXIST, pId,
                    pidComponentAdd.getComponentNos());
        }
        //数据组装
        List<ProductComponent> addList =pidComponentAdd.getComponentNos().stream().map(x->new ProductComponent(pId, x)).collect(Collectors.toList());
        //批量保存
        saveBatch(addList);
    }


    public void updateProductComponent(ProductComponentDto productComponentDto) {
        //校验产品是否存在
        productService.checkExistAndGetProductById(productComponentDto.getPid());
       //查询
        ProductComponent productComponent = getByPidAndComponent(productComponentDto.getPid(),productComponentDto.getComponentNo());
        productComponent.setSoftwareCoding(productComponentDto.getSoftwareCoding());
        //更新
        saveOrUpdate(productComponent);

    }

    @Override
    public void deleteComponent(Long pid, String componentNo) {
        //校验产品是否存在
        productService.checkExistAndGetProductById(pid);
        // 查看总成是否关联固件
        List<Firmware> list = firmwareService.getFirmwareByComponentNo(pid, componentNo);
        if (!CollectionUtils.isEmpty(list)) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_COMPONENT_ALREADY_EXIST_FIRMWARE);
        }
        //删除
        removeByPIdAndComponentNo(pid,componentNo);
    }

    /**
     * 根据产品Id和总成零件号删除
     */
    private void removeByPIdAndComponentNo(Long pid, String componentNo) {
        remove(Wrappers.<ProductComponent>lambdaQuery()
                .eq(ProductComponent::getProductId, pid)
                .eq(ProductComponent::getComponentNo, componentNo)
        );
    }

    @Override
    public List<ComponentVo> all(Long pid) {
        return componentMapper.getComponentVoByPid(pid);
    }
    @Override
    public List<ProductComponent> list(Long pid) {
        LambdaQueryWrapper<ProductComponent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductComponent::getProductId, pid);
        return this.list(wrapper);
    }

    @Override
    public void copy(Long sourcePid, Long targetPid) {
        List<ProductComponent> list = this.list(sourcePid);
        if (!CollectionUtils.isEmpty(list)) {
            List<ProductComponent> listResult = new ArrayList<>(list.size());
            list.forEach(component -> {
                ProductComponent draft = new ProductComponent(targetPid, component.getComponentNo());
                listResult.add(draft);
            });
            this.saveBatch(listResult);
        }

    }

    @Override
    public List<ProductComponent> getListByComponentNo(String componentNo) {
        LambdaQueryWrapper<ProductComponent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductComponent::getComponentNo, componentNo);
        return this.list(wrapper);
    }

    @Override
    public List<Component> getComponentsByPid(Long productId) {
        return productComponentMapper.getComponentsByPid(productId);
    }

    @Override
    public ComponentCheckVo check(CheckComponentDto checkComponentDto) {
        List<ComponentVo> list = this.all(checkComponentDto.getProductId());
        ComponentCheckVo check = null;
        for (ComponentVo componentVo : list) {
            if (componentVo.getComponentNo().equals(checkComponentDto.getComponentNo())) {
                check = new ComponentCheckVo();
                check.setComponentName(componentVo.getComponentName());
                check.setComponentType(componentVo.getComponentType());
                break;
            }
        }
        if (check == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMPONENT_NOT_EXIST, checkComponentDto.getComponentNo());
        }
        return check;
    }

    @Override
    public PageResult<ProductBaseInfoVo> getProductByComponentNo(SearchName name) {
        //分页获取记录
        Page<ProductBaseInfoVo> page = productComponentMapper.getProductInfoByComponentsNo(new Page<>(name.getPageNum(),
                name.getPageSize()), name.getName());

        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<ProductBaseInfoVo> products = page.getRecords();
            //获取品类多语言
            Map<Long, Long> categoryIds = products.stream().collect(Collectors.toMap(ProductBaseInfoVo::getPId, ProductBaseInfoVo::getCategoryId));
            Map<Long, CategoryVo> categoryVoMap = categoryService.getMyMap(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), categoryIds));
            //组装
            products.forEach(productBaseInfoVo -> {
                CategoryVo categoryVo = categoryVoMap.get(productBaseInfoVo.getPId());
                productBaseInfoVo.setCategoryName(null == categoryVo ? null : categoryVo.getCategoryName().getMessage());
            });
        }
        PageResult<ProductBaseInfoVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords());
        return res;
    }

    @Override
    public List<String> listComponentNosByPid(Long productId) {
        LambdaQueryWrapper<ProductComponent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductComponent::getProductId, productId).select(ProductComponent::getComponentNo);
        List<String> componentNos =  list(Wrappers.<ProductComponent>lambdaQuery()
                .eq(ProductComponent::getProductId, productId)
                .select(ProductComponent::getComponentNo))
                .stream()
                .map(ProductComponent::getComponentNo)
                .collect(Collectors.toList()
                );
        return componentNos;
    }
}
