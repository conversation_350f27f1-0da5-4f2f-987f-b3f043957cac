package com.chervon.technology.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.technology.domain.dataobject.DeviceFault;
import java.util.List;

/**
 * 设备故障实时状态表（61001）服务接口
 *
 * <AUTHOR>
 * @since 2024-03-08 16:02:05
 * @description 设备故障实时状态表（61001）服务接口
 */
public interface DeviceFaultService  extends IService<DeviceFault> {
    /**
     * 获取设备故障列表
     * @param deviceId 设备id
     * @param userId 用户id，可空
     * @return List<DeviceFaultVo>
     */
    List<DeviceFault> getDeviceFaultList(String deviceId,Long userId);

    /**
     * 更新设备故障状态
     * @param deviceId 设备id
     * @param faultCode 故障代码
     * @param status 故障状态
     * @return 是否有状态变更
     */
    boolean updateDeviceFaultStatus(String deviceId,String faultCode ,Integer status);
}
