package com.chervon.technology.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CacheConstants;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.BaseRemoteReqDto;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.prop.AwsProperties;
import com.chervon.common.core.utils.*;
import com.chervon.common.oss.uitl.S3Util;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import com.chervon.fleet.web.api.entity.dto.CompanyDeviceEditDto;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import com.chervon.iot.app.api.RemoteAppDeviceService;
import com.chervon.iot.app.api.RemoteDeviceInfoService;
import com.chervon.iot.app.api.vo.DeviceInfoRpcVo;
import com.chervon.iot.middle.api.dto.device.IotInsertDeviceDto;
import com.chervon.iot.middle.api.dto.group.AddDevice2GroupStaticDto;
import com.chervon.iot.middle.api.dto.log.TopologyPageDto;
import com.chervon.iot.middle.api.enums.DeviceEventType;
import com.chervon.iot.middle.api.enums.ThingModelType;
import com.chervon.iot.middle.api.service.*;
import com.chervon.iot.middle.api.vo.device.DeviceTopologyVo;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import com.chervon.iot.middle.api.vo.shadow.IotDeviceShadowItemVo;
import com.chervon.operation.api.RemoteBrandService;
import com.chervon.operation.api.RemoteCategoryService;
import com.chervon.operation.api.vo.BrandVo;
import com.chervon.operation.api.vo.CategoryVo;
import com.chervon.technology.api.dto.DeviceEditStatusDto;
import com.chervon.technology.api.dto.DeviceIdDto;
import com.chervon.technology.api.dto.DeviceRegisterDto;
import com.chervon.technology.api.enums.DeviceOnlineStatusEnum;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.enums.DeviceUsageStatusEnum;
import com.chervon.technology.api.exception.TechnologyErrorCode;
import com.chervon.technology.api.todevice.*;
import com.chervon.technology.config.DeviceMapConfig;
import com.chervon.technology.config.ExceptionMessageUtil;
import com.chervon.technology.config.IotPlatformCommon;
import com.chervon.technology.domain.bo.DictBo;
import com.chervon.technology.domain.bo.DictNodeBo;
import com.chervon.technology.domain.dataobject.*;
import com.chervon.technology.domain.dto.*;
import com.chervon.technology.domain.dto.device.DeviceEditDto;
import com.chervon.technology.domain.dto.device.SearchDeviceDto;
import com.chervon.technology.domain.dto.ota.ComponentOtaListDto;
import com.chervon.technology.domain.dto.ota.DeviceOtaDetailDto;
import com.chervon.technology.domain.dto.ota.DeviceOtaListDto;
import com.chervon.technology.domain.entity.OtaDeviceComponentResult;
import com.chervon.technology.domain.enums.ProductTypeEnum;
import com.chervon.technology.domain.vo.component.ComponentVo;
import com.chervon.technology.domain.vo.device.DeviceDetailVo;
import com.chervon.technology.domain.vo.device.DeviceExcel;
import com.chervon.technology.domain.vo.device.DeviceListVo;
import com.chervon.technology.domain.vo.ota.ComponentOtaListVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaDetailVo;
import com.chervon.technology.domain.vo.ota.DeviceOtaHistoryVo;
import com.chervon.technology.mapper.DeviceMapper;
import com.chervon.technology.service.*;
import com.chervon.technology.util.PageAssembler;
import com.chervon.technology.util.SignUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 20220706
 * 设备服务实现类
 */
@Service
@Slf4j
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {
    @Autowired
    private DeviceMapConfig deviceMapConfig;
    @Autowired
    private S3Util s3Util;
    @Autowired
    private AwsProperties awsProperties;

    @Autowired
    private DeviceCodeService deviceCodeService;
    @Lazy
    @Autowired
    private ProductService productService;
    @Autowired
    private DeviceMapService deviceMapService;
    @Autowired
    private ProductNetworkModeService productNetworkModeService;
    @Autowired
    private ProductComponentService productComponentService;
    @Autowired
    private ComponentService componentService;
    @Autowired
    private FirmwareService firmwareService;
    @Autowired
    private OtaDeviceResultService otaDeviceResultService;
    @Autowired
    private OtaDeviceComponentResultService otaDeviceComponentResultService;

    @DubboReference
    private RemoteDeviceService remoteDeviceService;
    @DubboReference
    private RemoteAppDeviceService remoteAppDeviceService;
    @DubboReference
    private RemoteDeviceLogService remoteDeviceLogService;
    @DubboReference
    private RemoteDeviceShadowService remoteDeviceShadowService;
    @DubboReference
    private RemoteOtaService remoteOtaService;
    @DubboReference
    private RemoteCategoryService remoteCategoryService;
    @DubboReference
    private RemoteBrandService remoteBrandService;
    @DubboReference
    private RemoteMultiLanguageService remoteMultiLanguageService;
    @DubboReference
    private RemoteDeviceInfoService remoteDeviceInfoService;

    @Autowired
    private DictService dictService;

    @Resource(name = "threadPoolBindTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @DubboReference
    private RemoteAwsGroupService remoteAwsGroupService;

    private static final String TIMEFORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    @Value("${quectel.cn.url}")
    private String cnUrl;
    @Value("${quectel.cn.appKey}")
    private String cnAppKey;
    @Value("${quectel.cn.secret}")
    private String cnSecret;
    @Value("${quectel.international.url}")
    private String inUrl;
    @Value("${quectel.international.appKey}")
    private String inAppKey;
    @Value("${quectel.international.secret}")
    private String inSecret;
    @Value("${url.northAmerica.awsUrl}")
    private String naAwsUrl;
    @Value("${url.northAmerica.serviceUrl}")
    private String naServiceUrl;
    @Value("${url.europe.awsUrl}")
    private String euAwsUrl;
    @Value("${url.europe.serviceUrl}")
    private String euServiceUrl;

    @Autowired
    private FlowChargingService flowChargingService;

    @Autowired
    private DeviceUserRefService deviceUserRefService;

    @DubboReference
    private RemoteFleetDeviceService remoteFleetDeviceService;


    @Autowired
    private DeviceDemoFileService deviceDemoFileService;

    /**
     * 设备注册同步锁
     */
    private final static String DEVICE_REGISTER_LOCK = "device_register_lock:";
    private final static String PATCH_SUFFIX=".patch";

    @Override
    public void editDevice(DeviceEditDto deviceEdit) {
        Device device = this.findByDeviceId(deviceEdit.getDeviceId());
        if (null == device) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, deviceEdit.getDeviceId());
        }
        Device updateDevice=new Device();
        updateDevice.setId(device.getId());
        if (StringUtils.isNotEmpty(deviceEdit.getDeviceNickname())) {
            updateDevice.setNickName(deviceEdit.getDeviceNickname());
        }
        // 如果APP把昵称置空,则云端device表更新昵称字段为商品型号型号
        if ("".equals(deviceEdit.getDeviceNickname())) {
            updateDevice.setNickName(device.getDeviceName());
        }
        if (StringUtils.isNotEmpty(deviceEdit.getDeviceIcon())) {
            updateDevice.setDeviceIcon(deviceEdit.getDeviceIcon());
        }
        this.updateById(updateDevice);
        log.info("update device info (editDevice) :{}",updateDevice);
    }

    @Override
    public DeviceTimeVo timeService(DeviceTimeDto deviceTime) {
        TimeZone tz = TimeZone.getTimeZone(deviceTime.getTimeZone());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        df.setTimeZone(tz);
        Date now = new Date();
        String strDate = df.format(now);
        log.info("timeService-->获取时间：{}", strDate);
        boolean b = tz.inDaylightTime(new Date());
        return new DeviceTimeVo(strDate, b, now.getTime());
    }

    @Override
    public Device findByDeviceId(String deviceId) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceId, deviceId)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Device findBySn(String sn) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<Device>()
                .eq(Device::getSn, sn)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public DeviceDetailVo getDetail(String deviceId) {
        DeviceDetailVo result = this.getBaseMapper().getDeviceDetail(deviceId);
        if (result == null) {
            return null;
        }
        result.setCommunicateMode(productNetworkModeService.getNetworkCodeByPid(result.getPid()));
        if (null != result.getCategoryName()) {
            CategoryVo categoryVo = remoteCategoryService.getDetail(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), Long.parseLong(result.getCategoryName())));
            result.setCategoryName(categoryVo.getCategoryName().getMessage());
        } else {
            result.setCategoryName(null);
        }
        if (null != result.getBrandName()) {
            BrandVo brandVo = remoteBrandService.getDetail(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), Long.parseLong(result.getBrandName())));
            result.setBrandName(brandVo.getBrandName().getMessage());
        } else {
            result.setBrandName(null);
        }
        // 获取设备注册信息(APP用户绑定设备后填写)
        DeviceInfoRpcVo deviceInfoRpcVo = remoteDeviceInfoService.getDeviceInfo(deviceId);
        if (deviceInfoRpcVo != null) {
            result.setRegisterTime(deviceInfoRpcVo.getPurchaseTime());
            result.setInfoRegisteredBy(deviceInfoRpcVo.getCreateBy());
        }
        return result;
    }

    @Override
    public Long getProductId(String deviceId) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceId, deviceId)
                .select(Device::getProductId)
                .last("limit 1");
        Device device = this.getOne(queryWrapper);
        if (device == null) {
            return null;
        }
        return device.getProductId();
    }

    @Override
    public PageResult<DeviceListVo> getDevicePage(SearchDeviceDto searchDevice) {
        Page<DeviceListVo> page = new Page<>(searchDevice.getPageNum(), searchDevice.getPageSize());
        List<DeviceListVo> deviceListVos = this.getBaseMapper().getDevicePage(page, searchDevice);
        PageResult res = PageAssembler.assemble(page);

        // 品牌品类
        Map<Long, Long> categoryIdMap = new HashMap<>(deviceListVos.size());
        List<Long> brandIdList = new ArrayList<>(deviceListVos.size());
        for (DeviceListVo vo : deviceListVos) {
            brandIdList.add(vo.getBrandName() == null ? null : Long.parseLong(vo.getBrandName()));
            categoryIdMap.put(vo.getPid(), vo.getCategoryName() == null ? null : Long.parseLong(vo.getCategoryName()));
        }
        Map<Long, CategoryVo> categoryMap = remoteCategoryService.getMyMap(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(),
                categoryIdMap));
        Map<Long, BrandVo> brandMap = remoteBrandService.getMyMap(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), brandIdList));
        deviceListVos.forEach(vo -> {
            CategoryVo categoryVo = categoryMap.get(vo.getPid());
            vo.setCategoryName(categoryVo == null ? null : categoryVo.getCategoryName().getMessage());
            BrandVo brandVo = brandMap.get(vo.getBrandName() == null ? null : Long.parseLong(vo.getBrandName()));
            vo.setBrandName(brandVo == null ? null : brandVo.getBrandName().getMessage());
        });
        res.setList(deviceListVos);
        return res;
    }

    @Override
    public List<DeviceExcel> getDeviceList(SearchDeviceDto searchDevice) {
        List<DeviceExcel> res = new ArrayList<>();
        List<DeviceListVo> deviceListVos = this.getBaseMapper().getDeviceList(searchDevice);
        if (CollectionUtils.isEmpty(deviceListVos)) {
            return res;
        }

        // 品牌品类
        Map<Long, Long> categoryIdMap = new HashMap<>(deviceListVos.size());
        List<Long> brandIdList = new ArrayList<>(deviceListVos.size());
        for (DeviceListVo vo : deviceListVos) {
            brandIdList.add(vo.getBrandName() == null ? null : Long.parseLong(vo.getBrandName()));
            categoryIdMap.put(vo.getPid(), vo.getCategoryName() == null ? null : Long.parseLong(vo.getCategoryName()));
        }
        Map<Long, CategoryVo> categoryMap = remoteCategoryService.getMyMap(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(),
                categoryIdMap));
        Map<Long, BrandVo> brandMap = remoteBrandService.getMyMap(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), brandIdList));
        deviceListVos.forEach(vo -> {
            CategoryVo categoryVo = categoryMap.get(vo.getPid());
            vo.setCategoryName(categoryVo == null ? null : categoryVo.getCategoryName().getMessage());
            BrandVo brandVo = brandMap.get(vo.getBrandName() == null ? null : Long.parseLong(vo.getBrandName()));
            vo.setBrandName(brandVo == null ? null : brandVo.getBrandName().getMessage());
        });
        List<DictBo> dictList = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Arrays.asList("operationDeviceTypeSearch", "deviceOnlineStatus", "deviceStatus", "deviceActiveStatus"));
        Map<String, DictBo> collect = dictList.stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));

        // 转化为excel
        deviceListVos.forEach(e -> {
            DeviceExcel excel = new DeviceExcel();
            excel.setDeviceId(e.getDeviceId());
            excel.setSn(e.getSn());
            excel.setPid(e.getPid());
            excel.setCategoryName(e.getCategoryName());
            excel.setBrandName(e.getBrandName());
            excel.setProductModel(e.getProductModel());
            excel.setCommodityModel(e.getCommodityModel());
            excel.setProductType(collect.get("operationDeviceTypeSearch").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getProductType()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setIsOnline(collect.get("deviceOnlineStatus").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getIsOnline().getValue()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setStatus(collect.get("deviceStatus").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getStatus().getValue()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setUsageStatus(collect.get("deviceActiveStatus").getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getUsageStatus().getValue()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription());
            excel.setActivationTime(DateTimeZoneUtil.format(e.getActivationTime(), searchDevice.getZone()));
            excel.setLastLoginTime(DateTimeZoneUtil.format(e.getLastLoginTime(), searchDevice.getZone()));
            res.add(excel);
        });
        return res;
    }

    @Override
    public void editDeviceStatus(DeviceEditStatusDto deviceEditStatusDto) {
        Device device = this.findByDeviceId(deviceEditStatusDto.getDeviceId());
        if (null == device) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, deviceEditStatusDto.getDeviceId());
        }
        // 调用中台接口修改设备实际状态
        remoteDeviceService.updateCertStatus(deviceEditStatusDto.getDeviceId(),
                deviceEditStatusDto.getStatus() == DeviceStatusEnum.NORMAL);
        // 修改设备数据库中状态
        Device updateStatus=new Device();
        updateStatus.setId(device.getId());
        updateStatus.setStatus(deviceEditStatusDto.getStatus());
        this.updateById(updateStatus);
        log.info("update device info (editDeviceStatus) :{}",updateStatus);
    }

    @Override
    public PageResult<ComponentVo> getComponentList(DeviceComponentListDto dto) {
        //1、根据设备Id查询产品Id
        Device device = this.getOne(Wrappers.<Device>lambdaQuery()
                .select(Device::getProductId)
                .eq(Device::getDeviceId, dto.getDeviceId())
        );
        if (null == device) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, dto.getDeviceId());
        }

        //2、从缓存中取出设备最新的总成零件缓存
        String key = RedisConstant.OTA_DEVICE_COMPONENT_MAP + dto.getDeviceId();
        Map<String, String> componentMap = RedisUtils.getCacheObject(key);
        if (CollectionUtil.isEmpty(componentMap)) {
            //2.1、查询iot core是否有总成信息
            try{
                componentMap = remoteOtaService.getAllFirmwareVersion(dto.getDeviceId());
            }catch (Exception e){
                log.error("查询设备总成信息失败，设备id：{}", dto.getDeviceId(), e);
                return null;
            }
            if(Objects.isNull(componentMap)||componentMap.isEmpty()){
                return null;
            }
            RedisUtils.setCacheObject(key,componentMap);
        }
        Set<String> componentNos = componentMap.keySet();
        if (CollectionUtils.isEmpty(componentNos)) {
            return null;
        }
        //3、从产品总成关系中筛选
        List<ProductComponent> productComponents=productComponentService.list(
                Wrappers.<ProductComponent>lambdaQuery()
                        .eq(ProductComponent::getProductId,device.getProductId())
                        .in(ProductComponent::getComponentNo,componentNos)
        );
        if(CollectionUtils.isEmpty(productComponents)){
            return null;
        }
        componentNos=productComponents.stream().map(ProductComponent::getComponentNo).collect(Collectors.toSet());


        //4、查询总成信息
        LambdaQueryWrapper<Component> componentLambdaQueryWrapper = new LambdaQueryWrapper<Component>()
                .in(Component::getComponentNo, componentNos);
        List<Component> componentList = componentService.list(componentLambdaQueryWrapper);
        Page<Component> page = new Page<>(dto.getPageNum(), dto.getPageSize());

        List<ComponentVo> componentVoList = ConvertUtil.convertList(componentList, ComponentVo.class);

        for (ComponentVo componentVo : componentVoList) {
            componentVo.setVersion(componentMap.get(componentVo.getComponentNo()));
        }
        //5、假分页?
        PageResult<ComponentVo> result = PageAssembler.assemble(page);
        result.setList(componentVoList);
        return result;
    }
    @Override
    public void export(SearchDeviceDto searchDeviceDto, HttpServletResponse response) throws IOException {
        try {
            List<DeviceExcel> data = this.getDeviceList(searchDeviceDto);
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new DeviceExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("Device-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @Override
    public PageResult<DeviceTopologyVo> getTopologicalDeviceList(DeviceTopologyQueryDto deviceTopologyQueryDto) {
        TopologyPageDto topologyPageDto = ConvertUtil.convert(deviceTopologyQueryDto, TopologyPageDto.class);
        PageResult<DeviceTopologyVo> pageResult = remoteDeviceLogService.pageDeviceTopology(topologyPageDto);
        // 连表查询设备名以及设备类型
        List<DeviceTopologyVo> target = pageResult.getList();
        List<String> deviceIds = target.stream().map(DeviceTopologyVo::getDeviceId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIds)) {
            return pageResult;
        }
        //下面的方法deviceIds必须不能为空才能调用
        List<DeviceTopologyVo> deviceNameAndProductTypeList = this.getBaseMapper().getDeviceNameAndProductTypeByDeviceIds(deviceIds);
        target = pageResult.getList().stream().peek(m -> deviceNameAndProductTypeList.stream().filter(m2 ->
                Objects.equals(m.getDeviceId(), m2.getDeviceId())).forEach(m2 -> {
            m.setDeviceName(m2.getDeviceName());
            m.setDeviceType(ProductTypeEnum.getLabelByValue(m2.getDeviceType()));
        })).collect(Collectors.toList());
        pageResult.setList(target);
        return pageResult;
    }

    @Override
    public PageResult<IotDeviceShadowItemVo> pageDeviceProperty(ThingModelListDto thingModelListDto) {
        PageResult<IotDeviceShadowItemVo> res = remoteDeviceShadowService.pageItemsWithThingModel(thingModelListDto,
                thingModelListDto.getDeviceId(), ThingModelType.PROPERTY);
        handleName(res);
        return res;
    }

    @Override
    public PageResult<IotDeviceShadowItemVo> pageDeviceService(ThingModelListDto thingModelListDto) {
        PageResult<IotDeviceShadowItemVo> res = remoteDeviceShadowService.pageItemsWithThingModel(thingModelListDto,
                thingModelListDto.getDeviceId(), ThingModelType.SERVICE);
        handleName(res);
        return res;
    }

    @Override
    public PageResult<IotDeviceShadowItemVo> pageDeviceEvent(ThingModelListDto thingModelListDto) {
        PageResult<IotDeviceShadowItemVo> res = remoteDeviceShadowService.pageItemsWithThingModel(thingModelListDto,
                thingModelListDto.getDeviceId(), ThingModelType.EVENT);
        handleName(res);
        return res;
    }

    private void handleName(PageResult<IotDeviceShadowItemVo> data) {
        if (data == null || CollectionUtil.isEmpty(data.getList())) {
            return;
        }
        List<String> names = data.getList().stream().map(IotDeviceShadowItemVo::getName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<MultiLanguageBo> bos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(names)) {
            bos = remoteMultiLanguageService.listByIds(names);
        }
        Map<Long, String> collect = bos.stream().collect(Collectors.toMap(MultiLanguageBo::getLangId, MultiLanguageBo::getLangCode));
        data.getList().forEach(e -> {
            try {
                if (StringUtils.isNotBlank(e.getName())) {
                    String langCode = collect.getOrDefault(Long.parseLong(e.getName()), "");
                    e.setName(com.chervon.technology.config.MultiLanguageUtil.getByLangCode(langCode, LocaleContextHolder.getLocale().getLanguage()));
                }
                e.setCallType("sync".equals(e.getCallType()) ? "是" : "否");
                e.setType(DeviceEventType.getZhByType(e.getType()));
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }

    @Override
    public Object getDeviceFunctionLog(DeviceFunctionLogDto dto) {
        return remoteDeviceLogService.getLastUpdateLog(dto.getDeviceId(), dto.getIdentifier(), dto.getIsReported());
    }

    @Override
    public boolean updateOnlineStatus(String deviceId, String status) {
        LambdaUpdateWrapper<Device> wrapper = new LambdaUpdateWrapper<>();
        DeviceOnlineStatusEnum online = "connected".equals(status) ? DeviceOnlineStatusEnum.ONLINE : DeviceOnlineStatusEnum.OFFLINE;
        wrapper.eq(Device::getDeviceId, deviceId).
                set(Device::getIsOnline, online.getValue());
        if (online == DeviceOnlineStatusEnum.ONLINE) {
            wrapper.set(Device::getLastLoginTime, LocalDateTime.now());
        } else {
            wrapper.set(Device::getLastLogoutTime, LocalDateTime.now());
        }
        boolean update = this.update(wrapper);
        // 判断设备是否在fleet，如果在fleet，同步状态到fleet；
        //目前fleet只有CH17硬网关需要更新在线离线状态
        if (!"CH17".equals(getSn(deviceId))) {
            return update;
        }
        DeviceUserRef deviceUserRef = deviceUserRefService.getOne(new LambdaQueryWrapper<DeviceUserRef>().eq(DeviceUserRef::getDeviceId, deviceId));
        if (deviceUserRef != null && deviceUserRef.getCurrentBindBusinessType() != null && 2 == deviceUserRef.getCurrentBindBusinessType()) {
            remoteFleetDeviceService.updateDeviceOnlineStatus(deviceId, "connected".equals(status) ? 1 : 0);
        }
        return update;
    }

    @Override
    public Boolean isOnline(String deviceId) {
        Device device = findByDeviceId(deviceId);
        if (Objects.nonNull(device) && Objects.equals(device.getIsOnline(), DeviceOnlineStatusEnum.ONLINE)) {
            return true;
        }
        return false;
    }

    public static String getSn(String deviceId){
        if(deviceId.length()<6){
            return "";
        }
        return deviceId.substring(1,5);
    }

    @Override
    public IotDeviceCertVo syncDeviceRegister(DeviceRegisterDto deviceRegisterDto) {
        String deviceRegisterLock = DEVICE_REGISTER_LOCK + deviceRegisterDto.getDeviceId();
        RLock lock = RedisUtils.getClient().getLock(deviceRegisterLock);
        boolean flag = true;
        try {
            flag = lock.tryLock(5L, 3L, TimeUnit.SECONDS);
            if (flag) {
                return deviceRegister(deviceRegisterDto);
            }
        } catch (InterruptedException e) {
            log.error("device register error",e);
        } finally {
            if (flag && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IotDeviceCertVo deviceRegister(DeviceRegisterDto deviceRegisterDto) {
        Device device = this.findByDeviceId(deviceRegisterDto.getDeviceId());
        boolean isExistDevice = (null != device);
        device = saveDeviceInfo(deviceRegisterDto, device);

        return awsRegisterDevice(deviceRegisterDto, device, isExistDevice);
    }

    @Override
    public void asyncAwsRegisterDevice(DeviceRegisterDto deviceRegisterDto, Device device, boolean isExistDevice) {
        IotInsertDeviceDto iotInsertDeviceDto = new IotInsertDeviceDto();
        iotInsertDeviceDto.setDeviceId(device.getDeviceId());
        Map<String, String> map = new HashMap<>(2);
        map.put(CommonConstant.DEVICE_SN, device.getSn());
        iotInsertDeviceDto.setAttributeMap(map);
        iotInsertDeviceDto.setProductKey(String.valueOf(deviceRegisterDto.getProductId()));
        //将设备添加到产品分组里
        iotInsertDeviceDto.setGroupName(String.valueOf(deviceRegisterDto.getProductId()));
        threadPoolTaskExecutor.execute(() -> {
            try {
                log.info("asyncAwsRegisterDevice-->设备{}绑定异步注册aws，调用iotCore创建设备、影子、证书信息等",deviceRegisterDto.getDeviceId(),deviceRegisterDto);
                long startTime = System.currentTimeMillis();
                doAsyncAwsRegister(deviceRegisterDto, device, isExistDevice, iotInsertDeviceDto);
                long endTime = System.currentTimeMillis();
                log.info("证书申请完成:->耗时毫秒：{}，申请设备：{}", endTime - startTime,deviceRegisterDto.getDeviceId());
            } catch (Exception e) {
                log.error("asyncAwsRegisterDevice-->设备绑定异步注册aws发生异常,request：{}是否已注册过设备：{}，异常：{},",deviceRegisterDto,isExistDevice,e);
            }
        });
    }

    private void doAsyncAwsRegister(DeviceRegisterDto deviceRegisterDto, Device device, boolean isExistDevice, IotInsertDeviceDto iotInsertDeviceDto) {
        //纯蓝牙设备不需要申请证书
        final boolean needApplyCertificate = deviceCodeService.isNeedApplyCertificateByPid(deviceRegisterDto.getProductId());
        if (isExistDevice) {
            if(needApplyCertificate){
                // 设备注册过，重新刷新证书（蓝牙设备无需证书，不用刷新）
                remoteDeviceService.updateDeviceCert(device.getDeviceId());
                log.info("设备绑定异步刷新证书完成：{}",device.getDeviceId());
            }
        } else {
            // 设备没注册过，创建设备，分组，影子，证书（蓝牙不需要）信息等到AWS平台
            remoteDeviceService.awsCreateDeviceInfo(iotInsertDeviceDto,needApplyCertificate);
            log.info("设备绑定异步创建设备和影子完成：{}，需申请证书否：{}",device.getDeviceId(),needApplyCertificate);
        }
    }

    private IotDeviceCertVo awsRegisterDevice(DeviceRegisterDto deviceRegisterDto, Device device, boolean isExistDevice) {
        // 调用RPC完成设备注册
        log.info("deviceReg-->设备注册，调用iotCore");
        IotInsertDeviceDto iotInsertDeviceDto = new IotInsertDeviceDto();
        iotInsertDeviceDto.setDeviceId(device.getDeviceId());
        Map<String, String> map = new HashMap<>(1);
        map.put(CommonConstant.DEVICE_SN, device.getSn());
        iotInsertDeviceDto.setAttributeMap(map);
        iotInsertDeviceDto.setProductKey(String.valueOf(deviceRegisterDto.getProductId()));
        if (isExistDevice && remoteDeviceService.checkAwsDeviceNameExisted(device.getDeviceId())) {
            // 设备注册过，重新刷新证书
            return remoteDeviceService.updateDeviceCert(device.getDeviceId());
        } else {
            // 设备没注册过，新增证书到AWS平台
            IotDeviceCertVo iotDeviceCertVo = remoteDeviceService.insertDeviceToAws(iotInsertDeviceDto);
            //添加设备至产品分组
            AddDevice2GroupStaticDto addDevice2GroupStaticDto = new AddDevice2GroupStaticDto();
            addDevice2GroupStaticDto.setDeviceId(deviceRegisterDto.getDeviceId());
            addDevice2GroupStaticDto.setGroupName(String.valueOf(deviceRegisterDto.getProductId()));
            remoteAwsGroupService.addDevice2StaticGroup(addDevice2GroupStaticDto);
            return iotDeviceCertVo;
        }
    }

    @Override
    public Device saveDeviceInfo(DeviceRegisterDto deviceRegisterDto,Product product) {
        Device device=new Device();
        device.setDeviceId(deviceRegisterDto.getDeviceId());
        // 更新设备表SN
        DeviceCode deviceCode = deviceCodeService.findNormalOneByDeviceId(deviceRegisterDto.getDeviceId());
        if (Objects.nonNull(deviceCode)) {
            device.setSn(deviceCode.getSn());
        }
        deviceRegisterDto.setDeviceNickName(product.getCommodityModel());
        deviceRegisterDto.setProductId(product.getId());
        device.setDeviceName(deviceRegisterDto.getDeviceNickName());
        device.setNickName(deviceRegisterDto.getDeviceNickName());
        device.setProductId(deviceRegisterDto.getProductId());
        device.setStatus(DeviceStatusEnum.NORMAL);
        device.setUsageStatus(DeviceUsageStatusEnum.INACTIVE);
        //检查和设置设备版本号
        setDeviceVersion(false, device);
        this.saveOrUpdate(device);
        return device;
    }


    private Device saveDeviceInfo(DeviceRegisterDto deviceRegisterDto, Device device) {
        boolean isExistDevice=true;
        if (Objects.isNull(device)) {
            device = new Device();
            device.setDeviceId(deviceRegisterDto.getDeviceId());
            isExistDevice=false;
        }
        // 更新设备表SN
        DeviceCode deviceCode = deviceCodeService.findNormalOneByDeviceId(deviceRegisterDto.getDeviceId());
        if (Objects.nonNull(deviceCode)) {
            device.setSn(deviceCode.getSn());
        }
        if (deviceRegisterDto.getProductId() == null) {
            // 查看所属产品是否存在
            Product product = productService.findBySnCode(deviceRegisterDto.getDeviceId().substring(1, 5));
            if (null == product) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST2, deviceRegisterDto.getDeviceId());
            }
            deviceRegisterDto.setDeviceNickName(product.getCommodityModel());
            deviceRegisterDto.setProductId(product.getId());
        }
        device.setDeviceName(deviceRegisterDto.getDeviceNickName());
        device.setNickName(deviceRegisterDto.getDeviceNickName());
        device.setProductId(deviceRegisterDto.getProductId());
        device.setStatus(DeviceStatusEnum.NORMAL);
        device.setUsageStatus(isExistDevice ? DeviceUsageStatusEnum.ACTIVE : DeviceUsageStatusEnum.INACTIVE);
        //检查和设置设备版本号
        setDeviceVersion(isExistDevice, device);
        this.saveOrUpdate(device);
        return device;
    }

    //检查并设置设备版本号-（解决设备未注册，就上报版本号问题）
    private void setDeviceVersion(boolean isRegistered, Device device) {
        if (isRegistered) {
            return;
        }
        // 设备表之前没有设备数据，需要设置设备版本号
        //缓存上报的固件版本
        String key = RedisConstant.OTA_DEVICE_COMPONENT_MAP + device.getDeviceId();
        Map<String, String> versionMap = RedisUtils.getCacheObject(key);
        if (Objects.isNull(versionMap)) {
            log.info("device:{} not upload version until now");
            return;
        }
        //版本号列表组装
        List<String> versionList = assembleVersionList(versionMap, device.getProductId());
        //设置技术版本号（list列表拼串，中划线分割）
        device.setTechnologyVersion(assembleTechnolgyVersionByVersionList(versionList));
        //设置显示版本号
        LambdaQueryWrapper<Product> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Product::getId, device.getProductId())
                .select(Product::getCreateTime);
        Product product = productService.getOne(lambdaQueryWrapper);
        device.setCustomVersion(assembleCustomVersion(versionList, product.getCreateTime()));
    }

    @Override
    public IotDeviceCertVo deviceRegisterVirtual(DeviceRegisterDto deviceRegisterDto) {
        Device device = this.findByDeviceId(deviceRegisterDto.getDeviceId());
        // 查看所属产品是否存在
        device.setStatus(DeviceStatusEnum.NORMAL);
        device.setUsageStatus(DeviceUsageStatusEnum.INACTIVE);
        this.saveOrUpdate(device);
        // 调用RPC完成设备注册
        log.info("deviceReg-->设备注册，调用iotCore");
        IotInsertDeviceDto iotInsertDeviceDto = new IotInsertDeviceDto();
        iotInsertDeviceDto.setDeviceId(device.getDeviceId());
        java.util.Map<String, String> map = new HashMap<>(1);
        map.put(CommonConstant.DEVICE_SN, device.getSn());
        iotInsertDeviceDto.setAttributeMap(map);
        iotInsertDeviceDto.setProductKey(String.valueOf(device.getProductId()));
        // 设备没注册过，新增证书到AWS平台
        return remoteDeviceService.insertDeviceToAws(iotInsertDeviceDto);
    }

    @Override
    public void deviceRegisterNoIot(DeviceRegisterDto deviceRegisterDto) {
        String sn = deviceRegisterDto.getSn();
        Device device = this.findBySn(sn);
        boolean isRegistered = (null != device);
        // 查看多码表中是否存在
        DeviceCode deviceCode = deviceCodeService.getBySn(deviceRegisterDto.getSn());
        if (null == deviceCode) {
            log.warn("deviceReg-->设备注册，设备deviceId在多码表中不存在：{}", deviceRegisterDto);
            device = isRegistered ? device : new Device();
            device.setDeviceId(deviceRegisterDto.getDeviceId());
            device.setSn(deviceRegisterDto.getSn());
        } else {
            device = ConvertUtil.convert(deviceCode, Device.class);
        }
        if (deviceRegisterDto.getProductId() == null) {
            // 查看所属产品是否存在
            Product product = productService.findBySnCode(deviceRegisterDto.getDeviceId().substring(1, 5));
            if (null == product) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST, deviceRegisterDto.getDeviceId());
            }
            deviceRegisterDto.setDeviceNickName(product.getCommodityModel());
            deviceRegisterDto.setProductId(product.getId());
        }
        device.setDeviceName(deviceRegisterDto.getDeviceNickName());
        device.setNickName(deviceRegisterDto.getDeviceNickName());
        device.setProductId(deviceRegisterDto.getProductId());
        device.setStatus(DeviceStatusEnum.NORMAL);
        device.setUsageStatus(isRegistered ? DeviceUsageStatusEnum.ACTIVE : DeviceUsageStatusEnum.INACTIVE);
        this.saveOrUpdate(device);
    }

    @Override
    public Boolean checkRegistered(DeviceIdDto deviceIdDto) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<Device>()
                .eq(Device::getDeviceId, deviceIdDto.getDeviceId());
        return this.count(wrapper) > 0;
    }

    @Override
    public IotDeviceVerifyVo deviceVerify(IotDeviceVerifyDto dto) {
        //根据srcData获取公钥
        String snCode = dto.getDeviceId().substring(1, 5);
        Product product = productService.findBySnCode(snCode);
        if (product == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_PRODUCT_NOT_EXIST2, dto.getDeviceId());
        }
        String publicKey = product.getPublicKey();
        IotDeviceVerifyVo iotDeviceVerifyVo = new IotDeviceVerifyVo();
        try {
            if (RsaUtils.verify(dto.getDeviceId(), publicKey, dto.getSign())) {
                //验签成功，生成Token放到Redis中
                String token = String.valueOf(UUID.randomUUID());
                RedisUtils.setCacheObject(CacheConstants.IOT_PLATFORM_DEVICE_VERIFY_TOKEN + token,
                        dto.getDeviceId(), Duration.ofDays(1));
                iotDeviceVerifyVo.setToken(token);
            } else {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_VERIFY_FAILED, dto.getDeviceId());
            }
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_VERIFY_FAILED, dto.getDeviceId());
        }
        return iotDeviceVerifyVo;
    }

    @Override
    public DeviceFlowVo flow(DeviceFlowDto req) {
        DeviceFlowVo res = new DeviceFlowVo();
        String iccid = req.getIccid();
        String totalUsed;
        if (iccid.startsWith(IotPlatformCommon.CN_ICCID_PREFIX)) {
            totalUsed = handleCn(iccid);
        } else if (iccid.startsWith(IotPlatformCommon.IN_ICCID_PREFIX)) {
            totalUsed = handleIn(iccid);
        } else {
            return res;
        }
        res.setTotalUsed(totalUsed);
        return res;
    }

    private String handleCn(String iccid) {
        RestTemplate restTemplate = new RestTemplate();
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        //提交参数设置
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();

        map.add("appKey", cnAppKey);
        map.add("method", "fc.function.dayflow.list");
        map.add("t", String.valueOf(new Date().getTime() / 1000));
        map.add("iccid", iccid);
        map.add("month", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));

        // 将参数加密签名
        String sourceData = SignUtils.makeSignData(map, cnSecret);
        String signData = SignUtils.shaEncode(sourceData);
        map.add("sign", signData);
        // 组装请求体
        HttpEntity<?> request = new HttpEntity<>(map, headers);
        JSONObject res;
        try {
            res = restTemplate.postForObject(cnUrl, request, JSONObject.class);
            System.out.println(res);
        } catch (Exception e) {
            log.error("syncFlowChargingJob handleExternal http error:{}", e.getMessage(), e);
            return null;
        }
        if (res != null && res.containsKey("flows")) {
            JSONArray flows = res.getJSONArray("flows");
            if (flows != null) {
                BigDecimal total = BigDecimal.ZERO;
                for (int i = 0, j = flows.size(); i < j; i++) {
                    JSONObject jsonObject = flows.getJSONObject(i);
                    if (jsonObject != null && jsonObject.containsKey("flow")) {
                        BigDecimal flow = jsonObject.getBigDecimal("flow");
                        if (flow != null) {
                            total = total.add(flow);
                        }
                    }
                }
                return total.toPlainString();
            }
        }
        return null;
    }

    private String handleIn(String iccid) {
        List<FlowCharging> list = flowChargingService.list(new LambdaQueryWrapper<FlowCharging>().eq(FlowCharging::getIccid, iccid)
                .likeRight(FlowCharging::getHandleTime, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"))));
        BigDecimal total = list.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getFlow())).map(e -> new BigDecimal(e.getFlow())).reduce(BigDecimal.ZERO, BigDecimal::add);
        return total.toPlainString();
    }

    @Deprecated
    private DeviceFlowVo handleBefore(DeviceFlowDto deviceFlowDto) {
        RestTemplate restTemplate = new RestTemplate();
        // 请求头设置,x-www-form-urlencoded格式的数据
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        //提交参数设置
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = new Date();
        String time = sdf.format(date);
        String secret;
        String url;
        time = time.replaceAll("-", "");
        map.add("t", String.valueOf(date.getTime() / 1000));

        // 根据国内外iccid发出不同请求
        if (deviceFlowDto.getIccid().startsWith(IotPlatformCommon.CN_ICCID_PREFIX)) {
            url = cnUrl;
            secret = cnSecret;
            map.add("appKey", cnAppKey);
            map.add("method", "fc.function.card.realtimestatus");
            map.add("iccid", deviceFlowDto.getIccid());
        } else if (deviceFlowDto.getIccid().startsWith(IotPlatformCommon.IN_ICCID_PREFIX)) {
            url = inUrl;
            secret = inSecret;
            map.add("appKey", inAppKey);
            map.add("month", time);
            map.add("method", "fc.function.queryMonthFlowDetail");
            map.add("iccid", deviceFlowDto.getIccid());
        } else {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_ICCID_PREFIX_INVALID, deviceFlowDto.getIccid());
        }

        // 将参数加密签名
        String sourceData = SignUtils.makeSignData(map, secret);
        String signData = SignUtils.shaEncode(sourceData);
        map.add("sign", signData);

        // 组装请求体
        HttpEntity<HashMap<String, Object>> request = new HttpEntity(map, headers);

        //发起请求
        Object responseBean = restTemplate.postForObject(url, request, Object.class);
        String responseBeanJsonStr = JsonUtils.toJsonString(responseBean);
        JSONObject jsonObject = JsonUtils.parseObject(responseBeanJsonStr, JSONObject.class);
        if (responseBean == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_FLOW_RESPONSE_NULL);
        }

        // 处理结果
        DeviceFlowVo result = new DeviceFlowVo();
        if (deviceFlowDto.getIccid().startsWith(IotPlatformCommon.CN_ICCID_PREFIX)) {
            result.setType(CommonConstant.ZERO);
            jsonObject = jsonObject.getJSONObject("realTimeStatus");
            result.setTotal(jsonObject.getString("totalFlowb"));
            result.setTotalUsed(jsonObject.getString("usedFlowb"));
            result.setSurplusFlow(jsonObject.getString("surpFlowb"));
        } else if (deviceFlowDto.getIccid().startsWith(IotPlatformCommon.IN_ICCID_PREFIX)) {
            result.setType(CommonConstant.ONE);
            jsonObject = jsonObject.getJSONObject("data");
            result.setTotal(jsonObject.getString("total"));
            result.setTotalUsed(jsonObject.getString("totalused"));
            result.setSurplusFlow(jsonObject.getString("surplusflow"));
        }
        return result;
    }

    @Override
    public List<java.util.Map<String, Object>> listMapByIds(List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return this.getBaseMapper().listMapByIds(deviceIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadMap(DeviceMapUploadDto req, MultipartFile file) throws Exception {
        long beginTs = System.currentTimeMillis();
        if (StringUtils.isBlank(req.getDeviceId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR);
        }
        if (req.getComplete() == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR);
        }
        Device device = this.findByDeviceId(req.getDeviceId());
        if (null == device) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, req.getDeviceId());
        }
        String originalFileName = file.getOriginalFilename();
        log.info("uploadMap 设备id:{}开始上传地图文件:文件名:{},文件大小：{}，请求报文：{}",device.getDeviceId(), originalFileName,file.getSize(),req);
        if(file.getSize()<100){
            //设备上传空地图
            saveDeviceMap(req.getDeviceId(),"No map",file.getSize(), "", originalFileName);
            return;
        }
        if (originalFileName == null) {
            return;
        }
        if (!originalFileName.endsWith(".bz2")) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_NAME_INVALID);
        }
        String filePre = deviceMapConfig.getPath() + File.separator + req.getDeviceId() + File.separator
                + LocalDateTime.now().format(DateTimeFormatter.ofPattern(TIMEFORMAT_YYYYMMDDHHMMSS)) + File.separator;
        log.info("uploadMap 设备id:{}--时间：{}-文件预签名filePre:{}",device.getDeviceId(), beginTs, filePre);
        // 删除本地文件
        new File(filePre).delete();
        // 新建文件夹
        new File(filePre).mkdirs();
        try {
            // 上传的是整图
            if (req.getComplete()) {
                if (originalFileName.endsWith(".patch.bz2")) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_COMPLETE_PATCH);
                }
                File tempF = new File(filePre + originalFileName);
                if (!tempF.exists()) {
                    tempF.createNewFile();
                }
                IOUtils.copy(file.getInputStream(), Files.newOutputStream(tempF.toPath()));
                // 解压
                String res = exeCmdWithReturn("bzip2 -kd " + filePre + originalFileName);
                log.debug("{}---complete--解压结果：{}", beginTs, res);
                String newUnzipFileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                log.debug("{}---complete--解压文件：{}是否存在：{}", beginTs, filePre + newUnzipFileName, new File(filePre + newUnzipFileName).exists());
                log.info("{}---complete--解压文件：{}的大小：{}", beginTs, filePre + newUnzipFileName, new File(filePre + newUnzipFileName).length());
                // 判断md5是否相同
                String md5 = DigestUtil.md5Hex(new File(filePre + newUnzipFileName));
                log.info("{}---complete--{} complete md5 {}", beginTs, filePre + newUnzipFileName, md5);
                if (StringUtils.isBlank(md5) || !StringUtils.equals(req.getMd5(), md5)) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_ERROR);
                }
                // 上传文件到s3
                String key = SnowFlake.nextId() + getEnd(newUnzipFileName);
                s3Util.uploadFile(awsProperties.getDefaultBucket().getName(), key, new File(filePre + newUnzipFileName), false);
                final DeviceMap updateDeviceMap = saveDeviceMap(req.getDeviceId(),"whole",file.getSize(), key, newUnzipFileName);
                log.info("uploadMap update deviceMap 地图更新：{}",updateDeviceMap);
                return;
            }
            // 上传的是差分
            if (!originalFileName.endsWith(".patch.bz2")) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_DIFF_NOT_PATCH);
            }

            String[] split = originalFileName.split("\\.patch\\.bz2");
            String uploadFileBase = split[0];
            log.debug("{}---diff--uploadFileBase:{}", beginTs, uploadFileBase);

            DeviceMap deviceMap = deviceMapService.getOne(new LambdaQueryWrapper<DeviceMap>().eq(DeviceMap::getDeviceId, req.getDeviceId()));
            if (deviceMap == null) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_NOT_HAVE_COMPLETE);
            }
            // 下载整图文件到本地
            s3Util.download(awsProperties.getDefaultBucket().getName(), deviceMap.getFileKey(), new File(filePre + deviceMap.getFileKey()));
            // 上传差分文件到服务器
            File tempF = new File(filePre + uploadFileBase + ".patch.bz2");
            if (!tempF.exists()) {
                tempF.createNewFile();
            }
            IOUtils.copy(file.getInputStream(), Files.newOutputStream(tempF.toPath()));
            // 解压
            String unzip = exeCmdWithReturn("bzip2 -kd " + filePre + uploadFileBase + ".patch.bz2");
            log.debug("{}---diff--解压结果：{}", beginTs, unzip);

            log.debug("{}---diff--解压文件：{}是否存在：{}", beginTs, filePre + uploadFileBase + PATCH_SUFFIX, new File(filePre + uploadFileBase + PATCH_SUFFIX).exists());
            log.debug("{}---diff--解压文件：{}的大小：{}", beginTs, filePre + uploadFileBase + PATCH_SUFFIX, new File(filePre + uploadFileBase + PATCH_SUFFIX).length());
            // 合并
            String newCompleteFileName = SnowFlake.nextId() + getEnd(uploadFileBase);
            String oldF = filePre + deviceMap.getFileKey();
            String newF = filePre + newCompleteFileName;
            String patchF = filePre + uploadFileBase + PATCH_SUFFIX;
            String patch = exeCmdWithReturn("bspatch " + oldF + " " + newF + " " + patchF);
            log.debug("{}---diff--合并结果：{}", beginTs, patch);

            log.debug("{}---diff--合并文件：{}是否存在：{}", beginTs, filePre + newCompleteFileName, new File(filePre + newCompleteFileName).exists());

            log.debug("{}---diff--合并文件：{}的大小：{}", beginTs, filePre + newCompleteFileName, new File(filePre + newCompleteFileName).length());

            // 判断md5是否相同
            String md5 = DigestUtil.md5Hex(new File(newF));
            log.info("{}-{}---diff--{} diff md5 {}",device.getDeviceId(), beginTs, newF, md5);
            if (StringUtils.isBlank(md5) || !StringUtils.equals(req.getMd5(), md5)) {
                throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_ERROR);
            }

            // 上传新完整地图文件到s3
            File newFile= new File(newF);
            s3Util.uploadFile(awsProperties.getDefaultBucket().getName(), newCompleteFileName,newFile , false);
            // 更新到数据库
            DeviceMap updateDiffMap = new DeviceMap();
            updateDiffMap.setDeviceId(deviceMap.getDeviceId());
            updateDiffMap.setFileKey(newCompleteFileName);
            updateDiffMap.setOriginalFilename(deviceMap.getFileKey());
            updateDiffMap.setFileSize(newFile.length());
            updateDiffMap.setFileType("diff");
            updateDiffMap.setId(deviceMap.getId());
            updateDiffMap.setUpdateTime(LocalDateTime.now());
            deviceMapService.updateById(updateDiffMap);
            log.info("uploadMap update deviceMap 地图更新：{}",updateDiffMap);
        } finally {
            // 删除本地文件
            new File(filePre).delete();
        }
    }

    @NotNull
    private DeviceMap saveDeviceMap(String deviceId,String fileType, Long fileSize, String fileKey, String fileName) {
        // 更新到数据库
        DeviceMap updateDeviceMap = new DeviceMap();
        updateDeviceMap.setDeviceId(deviceId);
        updateDeviceMap.setFileKey(fileKey);
        updateDeviceMap.setOriginalFilename(fileName);
        updateDeviceMap.setFileSize(fileSize);
        updateDeviceMap.setFileType(fileType);
        updateDeviceMap.setUpdateTime(LocalDateTime.now());
        DeviceMap deviceMap = deviceMapService.getOne(new LambdaQueryWrapper<DeviceMap>().eq(DeviceMap::getDeviceId, deviceId));
        if (deviceMap != null) {
            updateDeviceMap.setId(deviceMap.getId());
        }
        deviceMapService.saveOrUpdate(updateDeviceMap);
        return updateDeviceMap;
    }

    @Override
    public String uploadMapWithResult(DeviceMapUploadDto req, MultipartFile file) throws Exception {
        long l = System.currentTimeMillis();
        if (StringUtils.isBlank(req.getDeviceId())) {
            return "=>deviceId is empty";
        }
        if (req.getComplete() == null) {
            return "=>complete param is null";
        }
        StringBuilder sb=new StringBuilder();
        sb.append("=>complete param is :"+req.getComplete());
        Device device = this.findByDeviceId(req.getDeviceId());
        if (null == device) {
            return "=> findByDeviceId return null"+req.getDeviceId();
        }

        String originalFileName = file.getOriginalFilename();
        sb.append("=> originalFileName:" + originalFileName);
        sb.append("=> --originalFileName size:"+ file.getSize());

        if (!originalFileName.endsWith(".bz2")) {
            sb.append("=>device map file is not end with bz2;");
        }
        String filePre = deviceMapConfig.getPath() + File.separator + req.getDeviceId() + File.separator
                + LocalDateTime.now().format(DateTimeFormatter.ofPattern(TIMEFORMAT_YYYYMMDDHHMMSS)) + File.separator;

        // 删除本地文件
        new File(filePre).delete();
        // 新建文件夹
        new File(filePre).mkdirs();
        try {
            // 上传的是整图
            if (req.getComplete()) {
                if (originalFileName.endsWith(".patch.bz2")) {
                    sb.append("=>整图上传，上传的文件扩展名错误：差分文件后缀.patch.bz2;");
                    return sb.toString();
                }
                File tempF = new File(filePre + originalFileName);
                if (!tempF.exists()) {
                    tempF.createNewFile();
                }
                IOUtils.copy(file.getInputStream(), Files.newOutputStream(tempF.toPath()));
                // 解压
                sb.append("=>开始解压，执行cmd命令：bzip2 -kd "+ filePre + originalFileName);
                String res = exeCmdWithReturn("bzip2 -kd " + filePre + originalFileName);
                sb.append("=>整图解压结果："+ res);
                String newUnzipFileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                String unzipFilePath=filePre + newUnzipFileName;
                sb.append("=>整图解压后文件路径："+unzipFilePath);
                File newUnzipFile= new File(filePre + newUnzipFileName);
                sb.append("=>整图文件存在否："+newUnzipFile.exists());
                sb.append("=>整图文件大小："+newUnzipFile.length());

                // 判断md5是否相同
                String md5 = DigestUtil.md5Hex(new File(unzipFilePath));
                final boolean isEq = req.getMd5().equals(md5);
                sb.append("=>【整图生成md5编码】："+md5);
                sb.append("=>请求参数md5:"+req.getMd5()+"是否相同："+isEq);
                if(!isEq){
                    return sb.toString();
                }
                // 上传文件到s3
                String key = SnowFlake.nextId() + getEnd(newUnzipFileName);
                s3Util.uploadFile(awsProperties.getDefaultBucket().getName(), key, newUnzipFile, false);
                sb.append("=>整图上传到s3存储桶，key:"+key+"存储桶："+awsProperties.getDefaultBucket().getName());
                // 更新到数据库
                DeviceMap one = new DeviceMap();
                one.setDeviceId(req.getDeviceId());
                one.setFileKey(key);
                one.setOriginalFilename(newUnzipFileName);
                DeviceMap deviceMap = deviceMapService.getOne(new LambdaQueryWrapper<DeviceMap>().eq(DeviceMap::getDeviceId, req.getDeviceId()));
                if (deviceMap != null) {
                    one.setId(deviceMap.getId());
                }
                final boolean saved = deviceMapService.saveOrUpdate(one);
                sb.append("=>整图更新数据库完成："+saved);
                return sb.toString();
            }
            // 上传的是差分
            if (!originalFileName.endsWith(".patch.bz2")) {
                sb.append("=>差分上传，上传的文件扩展名错误："+originalFileName);
                return sb.toString();
            }

            String[] split = originalFileName.split("\\.patch\\.bz2");
            String uploadFileBase = split[0];
            sb.append("=>差分上传：uploadFileBase:"+ uploadFileBase);

            DeviceMap deviceMap = deviceMapService.getOne(new LambdaQueryWrapper<DeviceMap>().eq(DeviceMap::getDeviceId, req.getDeviceId()));
            if (deviceMap == null) {
                return " =>deviceMapService.getOne return null"+req.getDeviceId();
            }
            // 下载整图文件到本地
            s3Util.download(awsProperties.getDefaultBucket().getName(), deviceMap.getFileKey(), new File(filePre + deviceMap.getFileKey()));
            // 上传差分文件到服务器
            File tempF = new File(filePre + uploadFileBase + ".patch.bz2");
            if (!tempF.exists()) {
                tempF.createNewFile();
            }
            IOUtils.copy(file.getInputStream(), Files.newOutputStream(tempF.toPath()));
            // 解压
            sb.append("=>差分开始解压，执行cmd命令：bzip2 -kd "+ filePre + uploadFileBase + ".patch.bz2");
            String unzip = exeCmdWithReturn("bzip2 -kd " + filePre + uploadFileBase + ".patch.bz2");
            sb.append("=>差分解压结果："+ unzip);
            File partFile= new File(filePre + uploadFileBase + PATCH_SUFFIX);
            sb.append("=>差分解压文件存在否："+partFile.exists());
            sb.append("=>差分解压文件大小："+partFile.length());

            // 合并
            String newCompleteFileName = SnowFlake.nextId() + getEnd(uploadFileBase);
            String oldF = filePre + deviceMap.getFileKey();
            String newF = filePre + newCompleteFileName;
            String patchF = filePre + uploadFileBase + PATCH_SUFFIX;
            String cmd="bspatch " + oldF + " " + newF + " " + patchF;
            sb.append("=>差分执行合并命令："+cmd);
            String patch = exeCmdWithReturn(cmd);
            sb.append("=>差分执行合并命令结果："+patch);
            File allPartFile= new File(filePre + newCompleteFileName);
            sb.append("=>差分合并文件存在否："+allPartFile.exists());
            sb.append("=>差分合并文件大小："+allPartFile.length());

            // 判断md5是否相同
            String md5 = DigestUtil.md5Hex(new File(newF));
            final boolean isEq = req.getMd5().equals(md5);
            sb.append("=>【差分文件生成md5编码】："+md5);
            sb.append("=>差分请求参数md5:"+req.getMd5()+"是否相同："+isEq);
            if(!isEq){
                return sb.toString();
            }

            // 上传新完整地图文件到s3
            s3Util.uploadFile(awsProperties.getDefaultBucket().getName(), newCompleteFileName, new File(newF), false);
            sb.append("=>差分合并后上传到s3存储桶，key:"+newCompleteFileName+"存储桶："+awsProperties.getDefaultBucket().getName());
            // 更新到数据库
            DeviceMap one = new DeviceMap();
            one.setDeviceId(deviceMap.getDeviceId());
            one.setFileKey(newCompleteFileName);
            one.setOriginalFilename(deviceMap.getFileKey());
            one.setId(deviceMap.getId());
            final boolean update = deviceMapService.updateById(one);
            sb.append("=>差分图更新数据库完成："+ update);
        } finally {
            // 删除本地文件
            new File(filePre).delete();
            sb.append("=>删除本地文件："+ filePre);
        }
        return sb.toString();
    }

    private String getEnd(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return "." + filename.substring(dot + 1);
            }
        }
        return "";
    }

    private void exeCmd(String cmd) throws Exception {
        Runtime r = Runtime.getRuntime();
        Process p = r.exec(cmd);
        p.waitFor();
    }

    private String exeCmdWithReturn(String cmd) throws Exception {
        Runtime r = Runtime.getRuntime();
        Process p = r.exec(cmd);
        BufferedReader b = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        StringBuilder sb = new StringBuilder();
        while ((line = b.readLine()) != null) {
            sb.append(line).append(" ");
        }
        b.close();
        return sb.toString();
    }

    @Override
    public Device getCurrentFirmwareVersion(String deviceId) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Device::getCustomVersion).eq(Device::getDeviceId, deviceId);
        Device device = this.getOne(wrapper);
        return device;
    }


    @Override
    public String updateDeviceVersionV2(Long jobId, String deviceId, String version) {
        //获取本次升级成功的目标版本号
        final String customVersion = getCurrentTargetVersion(jobId, deviceId, version);
        log.info("updateDeviceVersionV2设备{}升级成功，获取的新版本号为:{} 获取时间：{}",deviceId, customVersion,System.currentTimeMillis());
        //查询设备版本
        Device device = this.getOne(
                Wrappers.<Device>lambdaQuery()
                        .eq(Device::getDeviceId, deviceId)
                        .eq(Device::getIsDeleted, CommonConstant.ZERO)
                        .select(Device::getId, Device::getTechnologyVersion, Device::getCustomVersion)
        );
        if (Objects.nonNull(device)) {
            if (StringUtils.equals(device.getCustomVersion(), customVersion)) {
                //版本没有变化
                log.info("updateDeviceVersionV2设备{}的版本没有变化，版本为:{}", deviceId, customVersion);
            }else{
                log.info("updateDeviceVersionV2设备{}的版本发生变化，原版本为:{}，准备更新新版本为:{}", deviceId, device.getCustomVersion(), customVersion);
            }
            Device updateDeviceVersion=new Device();
            updateDeviceVersion.setId(device.getId());
            updateDeviceVersion.setCustomVersion(customVersion);
            boolean success = this.updateById(updateDeviceVersion);
            log.info("updateDeviceVersionV2设备Id:{}的版本变化更新，更新结果：{}", deviceId, success);
            Device updatedDevice = getOne(Wrappers.<Device>lambdaQuery().eq(Device::getDeviceId, deviceId).eq(Device::getIsDeleted, CommonConstant.ZERO));
            log.info("updateDeviceVersionV2设备Id:{}的版本变化更新，查询后结果为：{}", deviceId, updatedDevice);
            //更新IoT Core 设备当前版本
            Map<String, String> customVersionMap = new HashMap<>();
            customVersionMap.put("customVersion", customVersion);
            remoteDeviceService.updateAttributes(deviceId, customVersionMap);
        }else {
            log.error("updateDeviceVersionV2设备{}不存在，升级无法更新版本", deviceId);
        }
        return customVersion;
    }

    private String getCurrentTargetVersion(Long jobId, String deviceId, String customVersion) {
        //取出设备上报版本号
        Map<String, String> componentVersionMap = RedisUtils.getCacheObject(RedisConstant.OTA_DEVICE_COMPONENT_MAP + deviceId);
        List<String> customVersionList = Lists.newArrayList();
        //查询升级任务配置的设备总成
        List<String> componentNos =
                otaDeviceComponentResultService.list(
                        Wrappers.<OtaDeviceComponentResult>lambdaQuery()
                                .eq(OtaDeviceComponentResult::getJobId, jobId)
                                .eq(OtaDeviceComponentResult::getDeviceId, deviceId)
                ).stream().map(x -> x.getComponentNo()).collect(Collectors.toList());

        // 组装固件版本号列表
        componentVersionMap.forEach((componentNo, version) -> {
            //设备总成存在于升级任务总成，以任务配置的版本号为准，不存在则以设备版本为准
            if (componentNos.contains(componentNo)) {
                String firmwareVersion = firmwareService.getFirmwareVersion(jobId, componentNo);
                customVersionList.add(componentNo + "#" + firmwareVersion);
            } else {
                customVersionList.add(componentNo + "#" + version);
            }
        });
        Collections.sort(customVersionList);

        String customVersionStr;
        if (customVersionList.size() == 1) {
            // 单MCU产品（如R），客户版本号显示MCU的完整版本号
            String version = customVersionList.get(0);
            customVersionStr = version.substring(version.indexOf(".") - 1);
        } else {
            customVersionStr = customVersion;
        }

        return customVersionStr;
    }

    @SuppressWarnings("Duplicates")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFirmwareVersion(String deviceId, Map<String, String> versionMap) {
        if (CollectionUtil.isEmpty(versionMap)) {
            log.error("updatePackageVersion请求 device:{},updateFirmwareVersion versionMap is empty", deviceId);
            return;
        }
        //缓存上报的固件版本
        String key = RedisConstant.OTA_DEVICE_COMPONENT_MAP + deviceId;
        RedisUtils.setCacheObject(key, versionMap);

        //查询设备信息
        Device device = getOne(Wrappers.<Device>lambdaQuery()
                .eq(Device::getDeviceId, deviceId)
                .select(Device::getId, Device::getTechnologyVersion, Device::getCustomVersion, Device::getProductId)
        );
        //判断设备存不存在
        if (Objects.isNull(device)) {
            log.error("updatePackageVersion请求 device表:{} is empty", deviceId);
            return;
        }
        Long productId = device.getProductId();
        // 判断产品存不存在
        Product product = productService.getById(productId);
        if (Objects.isNull(product)) {
            log.error("updatePackageVersion请求 product:{} is empty", productId);
            return;
        }
        // 版本号列表组装
        List<String> versionList = assembleVersionList(versionMap, productId);
        //拼接技术版本号和用户显示版本号（设备首次上线更新用户显示版本号）
        String technologyVersion = assembleTechnolgyVersionByVersionList(versionList);
        // 客户版本号
        String customVersion = assembleCustomVersion(versionList, product.getCreateTime());
        Device updateDevice=new Device();
        updateDevice.setId(device.getId());
        // 更新技术版本号
        if (!StringUtils.equals(device.getTechnologyVersion(), technologyVersion)) {
            updateDevice.setTechnologyVersion(technologyVersion);
        }
        // 设备首次上线更新用户显示版本号
        if (StringUtils.isBlank(device.getCustomVersion())) {
            updateDevice.setCustomVersion(customVersion);
        }
        boolean success = updateById(updateDevice);
        log.info("updatePackageVersion请求 (updateFirmwareVersion) 设备{}开始更新固件版本，更新前：{} 更新后为:{},更新结果：{}", deviceId,device.getCustomVersion() ,updateDevice,success);
    }

    /**
     * 初始化客户版本号
     * @param versionList 版本列表
     * @param createTime 创建时间
     * @return 客户版本号 yyMMddHH
     */
    @Override
    public String assembleCustomVersion(List<String> versionList, LocalDateTime createTime) {
        if(versionList.size() == 1) {
            // 如果是单MCU的产品（如R），展示MCU的完整版本号，如V1.0.2
            String version = versionList.get(0);
            return version.substring(version.indexOf(".") - 1);
        }
        return createTime
                .atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneOffset.ofHours(8))
                .format(new DateTimeFormatterBuilder().appendPattern("yyMMddHH").toFormatter());
    }

    // 组装技术版本号
    private String assembleTechnolgyVersionByVersionList(List<String> versionList) {
        StringBuilder technologyVersionBuilder = new StringBuilder();
        versionList.forEach(tech -> {
            technologyVersionBuilder.append(tech);
            technologyVersionBuilder.append("-");
        });

        technologyVersionBuilder.deleteCharAt(technologyVersionBuilder.length() - 1);
        return technologyVersionBuilder.toString();
    }

    // 组装版本列表
    private List<String> assembleVersionList(Map<String, String> versionMap,Long productId) {
        List versionList=Lists.newArrayList();
        for (String componentNo : versionMap.keySet()) {
            StringBuilder tempTechnologyVersionStringBuilder = new StringBuilder();
            ProductComponent productComponent = productComponentService.getByPidAndComponent(productId, componentNo);
            if (Objects.isNull(productComponent)) {
                log.error("product componentNo:{} not exist", componentNo);
                continue;
            }
            //1.拼接总成零件
            tempTechnologyVersionStringBuilder.append(componentNo).append("#");
            //2.软件编码拼接
            if (StringUtils.isNotBlank(productComponent.getSoftwareCoding())) {
                //软件编码不存在不拼接
                tempTechnologyVersionStringBuilder.append(productComponent.getSoftwareCoding()).append("_");
            }
            //3.版本号拼接
            String version = versionMap.get(componentNo);
            if (version.contains("_")) {
                version = version.split("_")[1];
            }
            tempTechnologyVersionStringBuilder.append(version == null ? StringUtils.EMPTY : version);
            versionList.add(tempTechnologyVersionStringBuilder.toString());
        }
        Collections.sort(versionList);
        return versionList;
    }

    @Override
    public PageResult<DeviceOtaHistoryVo> getOtaRecord(DeviceOtaListDto deviceOtaListDto) {
        PageResult<DeviceOtaHistoryVo> result =
                otaDeviceResultService.getOtaRecord(deviceOtaListDto);
        return result;
    }

    @Override
    public PageResult<DeviceOtaDetailVo> getOtaDetail(DeviceOtaDetailDto detailDto) {
        PageResult<DeviceOtaDetailVo> result = otaDeviceComponentResultService.getOtaDetail(detailDto);
        return result;
    }

    @Override
    public PageResult<ComponentOtaListVo> getComponentOtaList(ComponentOtaListDto dto) {
        PageResult<ComponentOtaListVo> result =
                otaDeviceComponentResultService.getComponentOtaList(dto);
        return result;
    }

    @Override
    public void batchUpdateDeviceSnByDeviceId(List<DeviceUpdateDto> dto) {
        //查找设备表中deviceIds是否存在
        List<String> deviceIds = dto.stream().map(x -> x.getDeviceId()).collect(Collectors.toList());
        Map<String, String> deviceIdSnMap = dto.stream().collect(Collectors.toMap(DeviceUpdateDto::getDeviceId, DeviceUpdateDto::getSn));
        List<Device> devices = list(Wrappers.<Device>lambdaQuery()
                .in(Device::getDeviceId, deviceIds));
        List<Device> noSnDevices=devices.stream().filter(x->StringUtils.isBlank(x.getSn())).collect(Collectors.toList());

        List<CompanyDeviceEditDto> listCompanyDeviceEdit = new ArrayList<>();
        for (Map.Entry<String,String> entry : deviceIdSnMap.entrySet()) {
            listCompanyDeviceEdit.add(new CompanyDeviceEditDto().setDeviceId(entry.getKey()).setDeviceSn(entry.getValue()).setModifier("operation-platform-sn-sync"));
        }
        for (Device device : noSnDevices) {
            device.setSn(deviceIdSnMap.get(device.getDeviceId()));
        }
        //批量更新
        updateBatchById(noSnDevices);
        //同步fleet设备信息
        log.info("管理后台开始异步调用fleet设备基础信息同步接口,请求同步内容：{}",listCompanyDeviceEdit);
        new Thread(() -> remoteFleetDeviceService.batchUpdateCompanyDeviceInfo(listCompanyDeviceEdit)).start();
    }

    @Override
    public void updateDeviceSnByDeviceId(String deviceId, String sn) {
        LambdaUpdateWrapper<Device> lambda = new UpdateWrapper<Device>().lambda();
        lambda.set(Device::getSn, sn)
                .eq(Device::getDeviceId, deviceId);
        this.update(lambda);
        //同步fleet设备信息
        new Thread(() -> remoteFleetDeviceService.updateCompanyDeviceInfo(
                new CompanyDeviceEditDto().setDeviceId(deviceId).setDeviceSn(sn).setModifier("operation-platform"))
        ).start();
    }

    @Override
    public void fixMissingDeviceSnFromDeviceCode() {
        getBaseMapper().fixMissingDeviceSnFromDeviceCode();
    }

    @Override
    public void uploadDemoFile(DeviceDemoFileUploadDto req, MultipartFile file)throws Exception{
        long beginTs = System.currentTimeMillis();
        checkUploadDemoFile(req);
        Device device = this.findByDeviceId(req.getDeviceId());
        if (null == device) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_NOT_EXIST, req.getDeviceId());
        }
        String originalFileName = file.getOriginalFilename();
        log.info("uploadDemoFile 设备id:{}开始上传文件:文件名:{},文件大小：{}，请求报文：{}",device.getDeviceId(), originalFileName,file.getSize(),req);

        if (originalFileName == null) {
            return;
        }
        if (!originalFileName.endsWith(".bz2")) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_NAME_INVALID);
        }
        String filePre = deviceMapConfig.getPath() + File.separator + req.getDeviceId() + File.separator+"tmpfile"+ File.separator
                + LocalDateTime.now().format(DateTimeFormatter.ofPattern(TIMEFORMAT_YYYYMMDDHHMMSS)) + File.separator;
        log.info("uploadDemoFile 设备id:{}--时间：{}-文件预签名filePre:{}",device.getDeviceId(), beginTs, filePre);
        // 删除本地文件
        new File(filePre).delete();
        // 新建文件夹
        new File(filePre).mkdirs();
        try {
            // 上传的是整图
            if (req.getComplete()) {
                if (originalFileName.endsWith(".patch.bz2")) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_COMPLETE_PATCH);
                }
                String tempFileName=filePre + originalFileName;
                File tempF = new File(tempFileName);
                if (!tempF.exists()) {
                    tempF.createNewFile();
                }
                // 然后使用try-with-resources解决资源泄漏问题
                try (InputStream inputStream = file.getInputStream();
                     OutputStream outputStream = Files.newOutputStream(tempF.toPath())) {
                    IOUtils.copy(inputStream, outputStream);
                }catch (IOException e){
                    log.error("{}---complete--文件上传失败", beginTs, e);
                }
                // 解压
                String res = exeCmdWithReturn("bzip2 -kd " + filePre + originalFileName);
                log.debug("{}---complete--解压结果：{}", beginTs, res);
                String newUnzipFileName = originalFileName.substring(0, originalFileName.lastIndexOf("."));
                log.debug("{}---complete--解压文件：{}是否存在：{}", beginTs, filePre + newUnzipFileName, new File(filePre + newUnzipFileName).exists());
                log.info("{}---complete--解压文件：{}的大小：{}", beginTs, filePre + newUnzipFileName, new File(filePre + newUnzipFileName).length());
                // 判断md5是否相同
                String md5 = DigestUtil.md5Hex(new File(filePre + newUnzipFileName));
                log.info("{}---complete--{} complete md5 {}", beginTs, filePre + newUnzipFileName, md5);
                if (StringUtils.isBlank(md5) || !StringUtils.equals(req.getMd5(), md5)) {
                    throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_DEVICE_MAP_FILE_ERROR);
                }
                // 上传文件到s3
                String key = SnowFlake.nextId() + getEnd(newUnzipFileName);
                s3Util.uploadFile(awsProperties.getDefaultBucket().getName(), key, new File(filePre + newUnzipFileName), false);

                // 更新到数据库
                DeviceDemoFile deviceDemoFile = new DeviceDemoFile();
                deviceDemoFile.setDeviceId(req.getDeviceId());
                deviceDemoFile.setFileKey(key);
                deviceDemoFile.setOriginalFilename(newUnzipFileName);
                deviceDemoFile.setFileSize(file.getSize());
                deviceDemoFile.setUpdateTime(LocalDateTime.now());
                deviceDemoFileService.saveOrUpdate(deviceDemoFile);

                log.info("uploadDemoFile 更新：{}",deviceDemoFile);
            }
        } finally {
            // 删除本地文件
            new File(filePre).delete();
        }
    }

    private void checkUploadDemoFile(DeviceDemoFileUploadDto req) {
        if (StringUtils.isBlank(req.getDeviceId())) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR);
        }
        if (req.getComplete() == null) {
            throw ExceptionMessageUtil.getException(TechnologyErrorCode.TECHNOLOGY_COMMON_PARAM_ERROR);
        }
    }


}
