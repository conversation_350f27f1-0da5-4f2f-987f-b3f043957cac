<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.DeviceDebugLogMapper">

    <select id="getDeviceList" resultType="com.chervon.technology.domain.vo.device.DeviceListVo"
            parameterType="com.chervon.technology.domain.dto.device.SearchDeviceDto">
        select d.device_id as deviceId,
        d.sn as sn,
        d.product_id as pid,
        p.model as productModel,
        p.commodity_model as commodityModel,
        p.product_type as productType,
        d.is_online as isOnline,
        d.status as status,
        d.usage_status as usageStatus,
        d.activation_user_id as activationUserId,
        d.activation_time as activationTime,
        d.last_login_time as lastLoginTime,
        p.category_id as categoryName,
        p.brand_id as brandName,
        d.phone_num as devicePhoneNum
        from iot_platform.device as d
        join iot_platform.product as p on d.product_id = p.id
        <where>
            d.is_deleted = 0
            <if test="param2.deviceId != null and param2.deviceId != ''">
                and d.device_id like concat('%', #{param2.deviceId}, '%')
            </if>
            <if test="param2.sn != null and param2.sn != ''">
                and d.sn like concat('%', #{param2.sn}, '%')
            </if>
            <if test="param2.pid != null and param2.pid != ''">
                and d.product_id like concat('%', #{param2.pid}, '%')
            </if>
            <if test="param2.categoryId != null">
                and p.category_id = #{param2.categoryId}
            </if>
            <if test="param2.brandId != null">
                and p.brand_id = #{param2.brandId}
            </if>
            <if test="param2.productModel != null and param2.productModel != ''">
                and p.model like concat('%', #{param2.productModel}, '%')
            </if>
            <if test="param2.commodityModel != null and param2.commodityModel != ''">
                and p.commodity_model like concat('%', #{param2.commodityModel}, '%')
            </if>
            <if test="param2.productType != null and param2.productType != ''">
                and p.product_type = #{param2.productType}
            </if>
            <if test="param2.isOnline != null">
                and d.status = #{param2.isOnline.value}
            </if>
            <if test="param2.status != null">
                and d.status = #{param2.status.value}
            </if>
            <if test="param2.usageStatus != null">
                and d.usage_status = #{param2.usageStatus.value}
            </if>
            <if test="param2.activationUserId != null and param2.activationUserId != ''">
                and d.activation_user_id like concat('%', #{param2.activationUserId}, '%')
            </if>
            <if test="param2.devicePhoneNum != null and param2.devicePhoneNum != ''">
                and d.phone_num = #{param2.devicePhoneNum}
            </if>
            <if test="param2.activationStartTime != null and param2.activationStartTime != ''">
                and d.activation_time &gt;= #{param2.activationStartTime}
            </if>
            <if test="param2.activationEndTime != null and param2.activationEndTime != ''">
                and d.activation_time &lt;= #{param2.activationEndTime}
            </if>
            <if test="param2.lastLoginStartTime != null and param2.lastLoginStartTime != ''">
                and d.last_login_time &gt;= #{param2.lastLoginStartTime}
            </if>
            <if test="param2.lastLoginEndTime != null and param2.lastLoginEndTime != ''">
                and d.last_login_time &lt;= #{param2.lastLoginEndTime}
            </if>
        </where>
    </select>


</mapper>
