<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.technology.mapper.DeviceCodeMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.chervon.technology.domain.dataobject.DeviceCode" id="deviceCodeMap">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="sn" column="sn"/>
        <result property="productSnCode" column="product_sn_code"/>
        <result property="mes" column="mes"/>
        <result property="moCode" column="mo_code"/>
        <result property="itemCode" column="item_code"/>
        <result property="productionDate" column="production_date"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="getDeviceIds" resultType="java.lang.String">
        select device_id
        from device_code
        where is_deleted = 0
    </select>

    <select id="getSns" resultType="java.lang.String">
        select sn
        from device_code
        where is_deleted = 0
    </select>
</mapper>