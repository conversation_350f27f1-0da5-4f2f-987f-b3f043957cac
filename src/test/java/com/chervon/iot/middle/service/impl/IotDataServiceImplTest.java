import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class IotDataServiceImplTest {
    @Autowired
    private IotDataServiceImpl iotDataService;

    // 原有测试方法保持不变
    @Test
    void shouldReturnNullWhenSQLException() {
        // Given
        String productKey = "product001";
        String deviceId = "device001";

        // ... 其余内容保持不变 ...
    }
}