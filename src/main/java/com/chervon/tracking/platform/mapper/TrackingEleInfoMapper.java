package com.chervon.tracking.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.tracking.platform.entity.TrackingEleInfo;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/2/9
 * @desc 描述
 */
@Mapper
public interface TrackingEleInfoMapper extends BaseMapper<TrackingEleInfo>, MPJBaseMapper<TrackingEleInfo> {

    /**
     * 描述：删除
     * @date 2024/8/27 9:07
     * @param eleId eleId
     * @param pageId pageId
     */
    @Delete("UPDATE t_tracking_ele_info SET is_deleted = 1 WHERE ele_id = #{eleId} AND page_id = #{pageId}")
    void deleteById(@Param("eleId") int eleId, @Param("pageId") int pageId);
}
