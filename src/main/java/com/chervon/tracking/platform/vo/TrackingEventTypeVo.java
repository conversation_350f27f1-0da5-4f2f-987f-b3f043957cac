package com.chervon.tracking.platform.vo;

import com.chervon.tracking.platform.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2023/1/12
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackingEventTypeVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 事件类型编码
     */
    private String eventTypeCode;

    /**
     * 事件类型名称
     */
    private String eventTypeName;

}
