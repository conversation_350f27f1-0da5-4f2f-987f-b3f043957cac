package com.chervon.tracking.platform.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.tracking.platform.entity.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/1/12
 * @desc 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrackingEventInfoVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;

    private Integer appId;

    /**
     * APP名称
     */
    private String appName;

    /**
     * 业务所属模块
     */
    private String busBelongModule;

    private Integer busBelongModuleId;

    /**
     * 模块
     */
    private String moduleName;

    private Integer moduleId;

    /**
     * 页面名称
     */
    private String pageName;

    private Integer pageId;

    private Integer modId;

    private Integer eleId;

    private String eleName;

    private String eventCode;

    /**
     * 事件扩展字段
     */
    private String expandFields;

    /**
     * 事件类型，曝光：exposure，点击：click，停留：duration
     */
    private String eventTypeCode;

    private String eventTypeName;

    /**
     * 事件名称英文名
     */
    private String eventNameEn;

    /**
     * 事件名称中文名
     */
    private String eventNameCn;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 事件属性英文名
     */
    private String eventAttrEn;

    /**
     * 事件属性中文名
     */
    private String eventAttrCn;

    /**
     * 事件属性描述
     */
    private String eventAttrDesc;

    /**
     * 事件状态，1：在线，0：离线
     */
    private Integer state;

    /**
     * 是否删除，1：删除，0：未删除
     */
    private Integer isDeleted;

    /**
     * 目标版本
     */
    private String targetVersion;

    /**
     * 埋点形式（WEB端、APP端）
     */
    private String devType;


    /**
     * 上线时间
     */
    @JsonDeserialize(using = LocalDateTimeSerializerConfig.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime releaseDate;

    /**
     * 备注
     */
    private String comments;
}
