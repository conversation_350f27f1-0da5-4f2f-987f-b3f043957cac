package com.chervon.tracking.platform.controller;

import com.chervon.tracking.platform.dto.*;
import com.chervon.tracking.platform.entity.PageResult;
import com.chervon.tracking.platform.entity.ReqSingleField;
import com.chervon.tracking.platform.service.ITrackingEventInfoService;
import com.chervon.tracking.platform.vo.TrackingEventInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/event")
@Slf4j
public class EventInfoController {

    private final ITrackingEventInfoService eventInfoService;

    public EventInfoController(ITrackingEventInfoService eventInfoService) {
        this.eventInfoService = eventInfoService;
    }

    /**
     * 添加埋点事件接口
     *
     * @param queryEventInfo
     * @return
     */
    @PostMapping("add")
    public void eventAdd(@RequestBody TrackingEventInfoDto queryEventInfo) {
        eventInfoService.addEvent(queryEventInfo);
    }

    /**
     * 查询埋点事件详情接口
     *
     * @param req
     * @return
     */
    @PostMapping("detail")
    public TrackingEventInfoVo getDetail(@RequestBody ReqSingleField<String> req) {
        return eventInfoService.getDetail(req.getId());
    }

    /**
     * 删除埋点事件接口
     *
     * @param req
     * @return
     */
    @PostMapping("delete")
    public void eventDelete(@RequestBody ReqSingleField<String> req) {
        eventInfoService.deleteEvent(req.getId());
    }


    /**
     * 编辑埋点事件接口
     *
     * @param queryEventInfo
     * @return
     */
    @PostMapping("edit")
    public void eventEdit(@RequestBody TrackingEventInfoDto queryEventInfo) {
        eventInfoService.editEvent(queryEventInfo);
    }


    /**
     * 分页查询埋点事件接口
     *
     * @param queryEventPageDto
     * @return
     */
    @PostMapping("page")
    public PageResult<TrackingEventInfoVo> eventInfoPage(@RequestBody QueryEventInfoPageDto queryEventPageDto) {

        return eventInfoService.page(queryEventPageDto);
    }


    /**
     * 更新埋点事件状态接口
     *
     * @param trackingEventInfoDto
     * @return
     */
    @PostMapping("changeState")
    public void changeState(@RequestBody TrackingEventInfoDto trackingEventInfoDto) {
        eventInfoService.changeState(trackingEventInfoDto);
    }


}
