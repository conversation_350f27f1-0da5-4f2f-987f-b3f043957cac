package com.chervon.tracking.platform.dto;

import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.chervon.tracking.platform.entity.PageRequest;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/2/14
 * @dec 描述
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryAppInfoPageDto extends PageRequest{


    private Integer appId;

    private String appCode;

    private String appName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updatedBy;


}
