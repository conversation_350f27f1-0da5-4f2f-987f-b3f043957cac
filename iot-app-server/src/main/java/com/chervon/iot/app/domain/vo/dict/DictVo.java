package com.chervon.iot.app.domain.vo.dict;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 14:53
 * @desc 描述
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictVo {

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典项集合")
    private List<DictNode> nodes = new ArrayList<>();


}
