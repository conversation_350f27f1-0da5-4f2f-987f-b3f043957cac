package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.iot.app.domain.dataobject.DeviceInfoSyncFail;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 质保同步失败记录service
 */
public interface DeviceInfoSyncFailService extends IService<DeviceInfoSyncFail> {
    /**
     * 查询没有处理的同步异常记录
     * @param limit
     * @return
     */
    List<DeviceInfoSyncFail> findNotDealSyncFail(int limit);

    /**
     * 批量更新同步异常记录状态
     * @param idList
     * @param dealStatus
     */
    void batchUpdateStatus(List<Long> idList, int dealStatus);

    /**
     * 处理异常同步集合
     * @param notDealSyncFail
     */
    void dealSyncFailList(List<DeviceInfoSyncFail> notDealSyncFail);

    /**
     * 保存同步失败记录
     * @param sn
     * @param sfUserId
     * @param msg
     * @param dealStatus
     */
    void saveSyncFailInfo(String sn, String sfUserId, String msg, Integer dealStatus);
}
