package com.chervon.iot.app.util;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> 2024/7/4
 */
public class AppStringUtils {
    public static List<String> countryList = Arrays.asList(
            "Afghanistan", "Aland Islands", "Albania", "Algeria", "American Samoa",
            "Andorra", "Angola", "Anguilla", "Antarctica", "Antigua and Barbuda",
            "Argentina", "Armenia", "Aruba", "Australia", "Austria", "Azerbaijan",
            "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium",
            "Belize", "Benin", "Bermuda", "Bhutan", "Bolivia", "Bosnia and Herzegovina",
            "Botswana", "Bouvet Island", "Brazil", "British Indian Ocean Territory",
            "Brunei Darussalam", "Bulgaria", "Burkina Faso", "Burundi", "Cambodia",
            "Cameroon", "Canada", "Cape Verde", "Cayman Islands", "Central African Republic",
            "Chad", "Chile", "China", "Christmas Island", "Cocos (Keeling) Islands",
            "Colombia", "Comoros", "Congo", "Congo", "Cook Islands", "Costa Rica",
            "Côte d'Ivoire", "Croatia", "Cuba", "Cyprus", "Czech Republic", "Denmark",
            "Djibouti", "Dominica", "Dominican Republic", "Ecuador", "Egypt",
            "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Ethiopia",
            "Falkland Islands", "Faroe Islands", "Fiji", "Finland", "France",
            "French Guiana", "French Polynesia", "French Southern Territories",
            "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Gibraltar",
            "Greece", "Greenland", "Grenada", "Guadeloupe", "Guam", "Guatemala",
            "Guernsey", "Guinea", "Guinea-Bissau", "Guyana", "Haiti",
            "Heard Island and McDonald Islands", "Holy See", "Honduras", "Hong Kong",
            "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq",
            "Ireland", "Isle of Man", "Israel", "Italy", "Jamaica", "Japan",
            "Jersey", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Korea",
            "Korea", "Kuwait", "Kyrgyzstan", "Lao People's Democratic Republic",
            "Latvia", "Lebanon", "Lesotho", "Liberia", "Libyan Arab Jamahiriya",
            "Liechtenstein", "Lithuania", "Luxembourg", "Macao", "Macedonia",
            "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta",
            "Marshall Islands", "Martinique", "Mauritania", "Mauritius", "Mayotte",
            "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro",
            "Montserrat", "Morocco", "Mozambique", "Myanmar", "Namibia", "Nauru",
            "Nepal", "Netherlands", "Netherlands Antilles", "New Caledonia",
            "New Zealand", "Nicaragua", "Niger", "Nigeria", "Niue", "Norfolk Island",
            "Northern Mariana Islands", "Norway", "Oman", "Pakistan", "Palau",
            "Palestinian Territory", "Panama", "Papua New Guinea", "Paraguay",
            "Peru", "Philippines", "Pitcairn", "Poland", "Portugal", "Puerto Rico",
            "Qatar", "Réunion", "Romania", "Russian Federation", "Rwanda",
            "Saint Helena", "Saint Kitts and Nevis", "Saint Lucia", "Saint Pierre and Miquelon",
            "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe",
            "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone",
            "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia",
            "South Africa", "South Georgia and the South Sandwich Islands", "Spain",
            "Sri Lanka", "Sudan", "Suriname", "Svalbard and Jan Mayen", "Swaziland",
            "Sweden", "Switzerland", "Syrian Arab Republic", "Taiwan,Province of China",
            "Tajikistan", "Tanzania,United Republic of", "Thailand", "Timor-Leste",
            "Togo", "Tokelau", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey",
            "Turkmenistan", "Turks and Caicos Islands", "Tuvalu", "Uganda",
            "Ukraine", "United Arab Emirates", "United Kingdom", "United States",
            "United States Minor Outlying Islands", "Uruguay", "Uzbekistan",
            "Vanuatu", "Venezuela", "Viet Nam", "Virgin Islands (British)",
            "Virgin Islands (U.S.)", "Wallis and Futuna", "Western Sahara",
            "Yemen", "Zambia", "Zimbabwe"
    );

    /**
     * 改进的国家名称匹配方法
     * @param input 用户输入（如"China mainland"）
     * @return 最佳匹配的国家名称
     */
    public static String findClosestMatch(String input,List<String> dictionary) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        // 预处理输入：转为小写并移除多余空格
        String processedInput = input.toLowerCase().trim();

        // 1. 首先尝试完全匹配或包含匹配
        String fullMatch = findFullMatch(processedInput,dictionary);
        if (fullMatch != null) {
            return fullMatch;
        }

        // 2. 尝试前缀匹配（特别处理"China"这样的短词）
        String prefixMatch = findPrefixMatch(processedInput,dictionary);
        if (prefixMatch != null) {
            return prefixMatch;
        }

        // 3. 使用改进的相似度算法作为后备
        return findSimilarMatch(processedInput,dictionary);
    }

    // 查找完全匹配或包含匹配
    private static String findFullMatch(String input,List<String> dictionary) {
        for (String country : dictionary) {
            String lowerCountry = country.toLowerCase();
            // 检查输入是否完全包含国家名称
            if (input.equals(lowerCountry) || input.contains(lowerCountry)) {
                return country; // 返回原始形式（保留大小写）
            }
        }
        return null;
    }

    // 查找前缀匹配（特别处理短词）
    private static String findPrefixMatch(String input,List<String> dictionary) {
        for (String country : dictionary) {
            String lowerCountry = country.toLowerCase();
            // 如果国家名称是输入的前缀（至少3个字符匹配）
            if (lowerCountry.length() >= 3 && input.startsWith(lowerCountry)) {
                return country;
            }
            // 或者输入是国家名称的前缀
            if (input.length() >= 3 && lowerCountry.startsWith(input)) {
                return country;
            }
        }
        return null;
    }

    // 使用改进的相似度算法
    private static String findSimilarMatch(String input,List<String> dictionary) {
        String bestMatch = null;
        double bestScore = -1;

        for (String country : dictionary) {
            String lowerCountry = country.toLowerCase();

            // 计算加权相似度得分
            double score = calculateWeightedScore(input, lowerCountry);

            if (score > bestScore) {
                bestScore = score;
                bestMatch = country;
            }
        }

        // 只返回得分高于阈值的结果
        return bestScore > 0.6 ? bestMatch : null;
    }

    // 计算加权相似度得分
    private static double calculateWeightedScore(String a, String b) {
        // 原始Levenshtein相似度（0-1）
        double lev = 1 - (double)levenshteinDistance(a, b) / Math.max(a.length(), b.length());

        // 单词匹配奖励
        double wordBonus = calculateWordBonus(a, b);

        // 前缀匹配奖励
        double prefixBonus = calculatePrefixBonus(a, b);

        // 组合得分（可调整权重）
        return 0.5 * lev + 0.3 * wordBonus + 0.2 * prefixBonus;
    }

    // 计算单词匹配奖励
    private static double calculateWordBonus(String a, String b) {
        String[] aWords = a.split("\\s+");
        String[] bWords = b.split("\\s+");

        double maxBonus = 0;
        for (String aw : aWords) {
            for (String bw : bWords) {
                if (aw.equals(bw)) {
                    // 完全匹配的单词给予高奖励
                    maxBonus = Math.max(maxBonus, 1.0);
                } else if (aw.length() > 3 && bw.length() > 3 &&
                        (aw.contains(bw) || bw.contains(aw))) {
                    // 部分包含给予中等奖励
                    maxBonus = Math.max(maxBonus, 0.5);
                }else{
                    //nothing to do
                }
            }
        }
        return maxBonus;
    }

    // 计算前缀匹配奖励
    private static double calculatePrefixBonus(String a, String b) {
        int minLen = Math.min(a.length(), b.length());
        if (minLen < 3) return 0;

        int commonPrefix = 0;
        while (commonPrefix < minLen &&
                a.charAt(commonPrefix) == b.charAt(commonPrefix)) {
            commonPrefix++;
        }

        // 共同前缀越长，奖励越高
        return (double)commonPrefix / minLen;
    }

    // 原始Levenshtein距离算法（保持不变）
    public static int levenshteinDistance(String a, String b) {
        int[][] dp = new int[a.length() + 1][b.length() + 1];

        for (int i = 0; i <= a.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= b.length(); j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= a.length(); i++) {
            for (int j = 1; j <= b.length(); j++) {
                int cost = (a.charAt(i - 1) == b.charAt(j - 1)) ? 0 : 1;
                dp[i][j] = Math.min(
                        Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1),
                        dp[i - 1][j - 1] + cost
                );
            }
        }
        return dp[a.length()][b.length()];
    }

    // 测试用例
    public static void main(String[] args) {
        String[] testCases = {
                "China mainland",
                "Christmas",
                "China",
                "Chin",
                "Chinese",
                "Christmas Island",
                "Mainland China"
        };

        for (String testCase : testCases) {
            String matched = findClosestMatch(testCase,countryList);
            System.out.printf("输入: %-20s => 匹配: %s%n", testCase, matched);
        }
    }

    public static String removeSymbols(String input) {
        // 定义正则表达式，匹配所有非数字的字符
        String pattern = "[^0-9]";
        // 使用空字符串替换匹配到的符号
        String result = input.replaceAll(pattern, "");
        return result;
    }

}
