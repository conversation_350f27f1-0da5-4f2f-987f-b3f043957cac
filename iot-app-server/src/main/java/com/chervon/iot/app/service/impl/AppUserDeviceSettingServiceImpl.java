package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.iot.app.domain.dataobject.AppUserDeviceSetting;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceSettingDto;
import com.chervon.iot.app.domain.vo.user.AppUserDeviceSettingVo;
import com.chervon.iot.app.mapper.AppUserDeviceSettingMapper;
import com.chervon.iot.app.service.AppUserDeviceSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 用户设备设置表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-02 18:26:31
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserDeviceSettingServiceImpl extends ServiceImpl<AppUserDeviceSettingMapper, AppUserDeviceSetting> implements  AppUserDeviceSettingService {

    @Override
    public AppUserDeviceSettingVo getUserDeviceSetting(AppUserDeviceSettingDto queryDto) {
        AppUserDeviceSetting appUserDeviceSetting = getAppUserDeviceSetting(queryDto);
        AppUserDeviceSettingVo result=new AppUserDeviceSettingVo();
        result.setDeviceId(queryDto.getDeviceId());
        result.setKey(queryDto.getKey());
        if(!Objects.isNull(appUserDeviceSetting)){
            result.setValue(appUserDeviceSetting.getValue());
            result.setRemoteControlPractice(appUserDeviceSetting.getRemoteControlPractice());
            result.setGuideStep(appUserDeviceSetting.getGuideStep());
        }
        return result;
    }

    @Override
    public boolean updateUserDeviceSetting(AppUserDeviceSettingDto updateDto) {
        final AppUserDeviceSetting appUserDeviceSetting = getAppUserDeviceSetting(updateDto);
        AppUserDeviceSetting setting=new AppUserDeviceSetting();
        if(Objects.isNull(appUserDeviceSetting)){
            //新增设置
            setting.setDeviceId(updateDto.getDeviceId());
            setting.setUserId(updateDto.getUserId());
            setting.setRemoteControlPractice(updateDto.getRemoteControlPractice());
            setting.setGuideStep(updateDto.getGuideStep());
            setting.setKey(updateDto.getKey());
            setting.setValue(updateDto.getValue());
        }else{
            setting.setId(appUserDeviceSetting.getId());
            setting.setValue(updateDto.getValue());
            if(!Objects.isNull(updateDto.getRemoteControlPractice())){
                setting.setRemoteControlPractice(updateDto.getRemoteControlPractice());
            }
            if(!Objects.isNull(updateDto.getGuideStep())){
                setting.setGuideStep(updateDto.getGuideStep());
            }
        }
        return saveOrUpdate(setting);
    }

    private AppUserDeviceSetting getAppUserDeviceSetting(AppUserDeviceSettingDto queryDto) {
        LambdaQueryWrapper<AppUserDeviceSetting> queryWrapper=new LambdaQueryWrapper<AppUserDeviceSetting>()
                .eq(AppUserDeviceSetting::getDeviceId, queryDto.getDeviceId())
                .eq(AppUserDeviceSetting::getUserId, queryDto.getUserId())
                .eq(StringUtils.isNotEmpty(queryDto.getKey()),AppUserDeviceSetting::getKey,queryDto.getKey())
                .orderByDesc(AppUserDeviceSetting::getCreateTime);
        final List<AppUserDeviceSetting> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }
}