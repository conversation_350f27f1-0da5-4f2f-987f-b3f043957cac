package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.iot.app.domain.dataobject.AppUserDeviceSetting;
import com.chervon.iot.app.domain.dto.device.AppUserDeviceSettingDto;
import com.chervon.iot.app.domain.vo.user.AppUserDeviceSettingVo;
import com.chervon.iot.app.mapper.AppUserDeviceSettingMapper;
import com.chervon.iot.app.service.AppUserDeviceSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 用户设备设置表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-02 18:26:31
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserDeviceSettingServiceImpl extends ServiceImpl<AppUserDeviceSettingMapper, AppUserDeviceSetting> implements  AppUserDeviceSettingService {

    @Override
    public AppUserDeviceSettingVo getUserDeviceSetting(AppUserDeviceSettingDto queryDto) {
        final AppUserDeviceSetting appUserDeviceSetting = getAppUserDeviceSetting(queryDto);
        if(!Objects.isNull(appUserDeviceSetting)){
            final AppUserDeviceSettingVo vo = BeanCopyUtils.copy(appUserDeviceSetting, AppUserDeviceSettingVo.class);
            return vo;
        }
        return null;
    }

    @Override
    public boolean updateUserDeviceSetting(AppUserDeviceSettingDto updateDto) {
        final AppUserDeviceSetting appUserDeviceSetting = getAppUserDeviceSetting(updateDto);
        AppUserDeviceSetting operateDto=new AppUserDeviceSetting();
        if(Objects.isNull(appUserDeviceSetting)){
            //新增设置
            operateDto.setDeviceId(updateDto.getDeviceId());
            operateDto.setUserId(updateDto.getUserId());
            operateDto.setRemoteControlPractice(updateDto.getRemoteControlPractice());
            operateDto.setGuideStep(updateDto.getGuideStep());
        }else{
            operateDto.setId(appUserDeviceSetting.getId());
            if(!Objects.isNull(updateDto.getRemoteControlPractice())){
                operateDto.setRemoteControlPractice(updateDto.getRemoteControlPractice());
            }
            if(!Objects.isNull(updateDto.getGuideStep())){
                operateDto.setGuideStep(updateDto.getGuideStep());
            }
        }
        return saveOrUpdate(operateDto);
    }

    private AppUserDeviceSetting getAppUserDeviceSetting(AppUserDeviceSettingDto queryDto) {
        LambdaQueryWrapper<AppUserDeviceSetting> queryWrapper=new LambdaQueryWrapper<AppUserDeviceSetting>()
                .eq(AppUserDeviceSetting::getDeviceId, queryDto.getDeviceId())
                .eq(AppUserDeviceSetting::getUserId, queryDto.getUserId());
        final AppUserDeviceSetting appUserDeviceSetting = getOne(queryWrapper);
        return appUserDeviceSetting;
    }
}