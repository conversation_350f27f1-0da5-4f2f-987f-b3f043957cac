package com.chervon.iot.app.domain.dto.explore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 新品DTO
 *
 * <AUTHOR>
 */
@Data
public class NewProductDto implements Serializable {

    private static final long serialVersionUID = 7483979864840803149L;

    /**
     * 产品品类
     */
    @ApiModelProperty("产品品类")
    private String productCategory;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 产品型号
     */
    @ApiModelProperty("商品型号")
    private String commodityModel;

    /**
     * 购买链接
     */
    @ApiModelProperty("购买链接")
    private String shopUrl;

    /**
     * 产品图片链接
     */
    @ApiModelProperty("产品图片链接")
    private String imageUrl;
}
