package com.chervon.iot.app.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/6 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_agreement")
public class AppUserAgreement extends BaseDo implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 用户协议id
     */
    private Long userAgreementId;

    /**
     * 用户协议版本
     */
    private String userAgreementVersion;

    /**
     * 隐私协议id
     */
    private Long secretAgreementId;

    /**
     * 隐私协议版本
     */
    private String secretAgreementVersion;

    /**
     * 同意时间
     */
    private LocalDateTime agreeTime;

    /**
     * 撤销时间
     */
    private LocalDateTime withdrawTime;

    /**
     * 状态， 1  同意  2  撤销
     */
    private Integer status;

}
