package com.chervon.iot.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Data
@RefreshScope
@ConfigurationProperties(prefix = "explore")
public class ExploreNewProductProperties {

    private Map<String, Object> newProduct;

}
