package com.chervon.iot.app.domain.vo.device;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022-09-02 19:52
 **/
@Data
public class AppDeviceStatusVo implements Serializable {
    /**
     * 设备状态: 0 停用 1 启用
     */
    @ApiModelProperty("设备状态: 0 停用 1 启用")
    private Integer deviceStatus;
    /**
     * 设备多码状态： 0 废弃 1 启用
     */
    @ApiModelProperty("设备多码状态： 0 废弃 1 启用")
    private Integer deviceCodeStatus;
    /**
     * 设备在线状态： 0 离线 1 在线
     */
    @ApiModelProperty("设备在线状态： 0 离线 1 在线")
    private Integer isOnline;
}
