package com.chervon.iot.app.rpc;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.RemoteAppUserStateService;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.operation.api.RemoteOperationMessageService;
import com.chervon.usercenter.api.dto.enums.AppPresenceEnum;
import com.chervon.usercenter.api.service.UserCommandService;
import com.chervon.usercenter.api.vo.UserVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @date 2023/4/18 15:20
 */
@DubboService
@Slf4j
public class RemoteAppUserStateServiceImpl implements RemoteAppUserStateService {

    @DubboReference
    private UserCommandService userCommandService;

    @DubboReference
    private RemoteOperationMessageService remoteOperationMessageService;

    @Override
    public Long getUserIdByShortToken(String shortToken) {
        UserVo user;
        try {
            String token = RedisUtils.getCacheObject(shortToken);
            user = RedisUtils.getCacheObject(token);
        } catch (Exception ex) {
            log.error(AppErrorCode.APP_GET_USER_FAIL.getErrorMessage(), ex);
            return null;
        }
        if (user == null || user.getId() == null) {
            log.error(AppErrorCode.APP_GET_USER_FAIL.getErrorMessage());
            return null;
        }
        return user.getId();
    }

    @Override
    public void editUserPresenceState(String shortToken, String state) {
        Long userId = getUserIdByShortToken(shortToken);
        if (userId == null) {
            log.error(AppErrorCode.APP_SET_USER_STATE_FAIL.getErrorMessage());
            return;
        }
        // 设置用户的在线状态为在线
        userCommandService.editUserPresenceState(userId, state);
        if (AppPresenceEnum.ONLINE.getDesc().equals(state)) {
            // 远程调用检测是否推送系统/营销消息
            remoteOperationMessageService.checkSysAndMarketingMessage(userId);
        }
    }
}
