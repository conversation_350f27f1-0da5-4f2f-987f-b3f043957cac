package com.chervon.iot.app.domain.dto.explore;

import com.chervon.iot.app.domain.dto.device.DeviceShortInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDataDto {

    /**
     * 用户类型 0:T0用户，1：T1用户，2：T2用户，3：T3用户
     */
    private Integer type;

    /**
     * 用户设备列表
     */
    private List<DeviceShortInfo> deviceList;
}
