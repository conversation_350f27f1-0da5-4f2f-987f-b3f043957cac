package com.chervon.iot.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.iot.app.domain.dataobject.DeviceRunRecord;
import com.chervon.iot.app.domain.vo.device.DeviceRunDateVo;
import com.chervon.iot.middle.api.vo.log.DeviceRunParamVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-13 16:56
 **/
public interface DeviceRunDataService {
	/**
	 * 通过设备ID绑定设备
	 *
	 * @return 绑定成功后的一些判定
	 */
	DeviceRunDateVo getDeviceRunDate(DeviceRunParamVo deviceRunParamVo);

	/**
	 * 通过设备ID绑定设备
	 *
	 * @return 绑定成功后的一些判定
	 */
	DeviceRunDateVo saveDeviceRunDate(DeviceRunRecord deviceRunRecord);

	/**
	 * 通过设备ID绑定设备
	 *
	 * @return 绑定成功后的一些判定
	 */
	Page<DeviceRunRecord> getList(DeviceRunParamVo deviceRunParamVo);


}

