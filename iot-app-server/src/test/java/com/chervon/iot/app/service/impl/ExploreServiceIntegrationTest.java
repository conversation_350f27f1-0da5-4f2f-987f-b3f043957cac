package com.chervon.iot.app.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.chervon.common.redis.constant.RedisConstant;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.config.ExploreProperties;
import com.chervon.iot.app.domain.dataobject.promotion.PromotionProduct;
import com.chervon.iot.app.domain.dto.device.DeviceShortInfo;
import com.chervon.iot.app.domain.dto.explore.ExploreBannerDto;
import com.chervon.iot.app.domain.dto.explore.UserDataDto;
import com.chervon.iot.app.service.ExploreService;
import com.chervon.iot.app.service.HaveFunService;
import com.chervon.iot.app.service.PromotionProductService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.DeviceListInfoRpcVo;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;
import java.util.List;

import static com.chervon.iot.app.service.impl.ExploreServiceTestDataBuilder.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExploreService集成测试
 * 专门测试涉及Redis缓存操作的方法，使用真实的Spring容器环境
 * 
 * <AUTHOR>
 */
@SpringBootTest
@DisplayName("ExploreService集成测试")
class ExploreServiceIntegrationTest {

    @Autowired
    private ExploreService exploreService;

    @MockBean
    private PromotionProductService promotionProductService;

    @MockBean
    private ExploreProperties exploreProperties;

    @MockBean
    private RemoteDeviceManageService remoteDeviceManageService;

    private MockedStatic<RedisUtils> redisUtilsMock;

    /**
     * 初始化MyBatis-Plus Lambda缓存
     */
    public static void initEntityTableInfo(Class<?>... entityClasses) {
        for (Class<?> entityClass : entityClasses) {
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), entityClass);
        }
    }

    @BeforeEach
    void setUp() {
        // Given - 初始化Lambda缓存
        initEntityTableInfo(PromotionProduct.class);

        // Given - 初始化Redis静态Mock
        redisUtilsMock = Mockito.mockStatic(RedisUtils.class);

        // Given - 设置默认Mock行为
        lenient().when(promotionProductService.selectByPromotionProductCategory(MOWERS_CATEGORY))
                .thenReturn(createPromotionProducts(MOWERS_CATEGORY, "Mower Product"));
        lenient().when(promotionProductService.selectByPromotionProductCategory(BLOWERS_CATEGORY))
                .thenReturn(createPromotionProducts(BLOWERS_CATEGORY, "Blower Product"));
        lenient().when(promotionProductService.selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY))
                .thenReturn(createPromotionProducts(STRING_TRIMMERS_CATEGORY, "Trimmer Product"));
        lenient().when(promotionProductService.selectByPromotionProductCategory(LIFESTYLE_CATEGORY))
                .thenReturn(createPromotionProducts(LIFESTYLE_CATEGORY, "Lifestyle Product", 2));
        lenient().when(exploreProperties.getNewProduct()).thenReturn(createNewProductConfig());
        lenient().when(exploreProperties.getUnified()).thenReturn("0");
    }

    @AfterEach
    void tearDown() {
        // 清理静态Mock
        if (redisUtilsMock != null) {
            redisUtilsMock.close();
        }
    }

    /**
     * 用户类型判断集成测试
     */
    @Nested
    @DisplayName("用户类型判断集成测试")
    class UserTypeIntegrationTests {

        @Test
        @DisplayName("T0用户类型判断 - Redis缓存为空应返回T0用户类型")
        void testGetExploreHomeData_T0User_EmptyCache() {
            // Given - 准备T0用户场景（Redis缓存为空）
            Long userId = TEST_USER_ID;
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(null);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "T0用户推荐结果不能为空");
            ExploreServiceTestUtils.verifyT0UserRecommendation(result);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("T1用户类型判断 - Redis缓存包含1个设备应返回T1用户推荐")
        void testGetExploreHomeData_T1User_SingleDevice() {
            // Given - 准备T1用户场景（1个设备）
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> t1Devices = createT1UserDevices();
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(t1Devices);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "T1用户推荐结果不能为空");
            ExploreServiceTestUtils.verifyT1UserRecommendation(result, true);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("T2用户类型判断 - Redis缓存包含2个设备应返回T2用户推荐")
        void testGetExploreHomeData_T2User_TwoDevices() {
            // Given - 准备T2用户场景（2个设备）
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> t2Devices = createT2UserDevices();
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(t2Devices);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "T2用户推荐结果不能为空");
            ExploreServiceTestUtils.verifyT2UserRecommendation(result, true, true);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("T3用户类型判断 - Redis缓存包含3个设备应返回T3用户推荐")
        void testGetExploreHomeData_T3User_ThreeDevices() {
            // Given - 准备T3用户场景（3个设备）
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> t3Devices = createT3UserDevices();
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(t3Devices);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "T3用户推荐结果不能为空");
            ExploreServiceTestUtils.verifyT3UserRecommendation(result, true);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("用户类型判断 - 设备列表包含充电器应被过滤")
        void testGetExploreHomeData_FilterChargerDevices() {
            // Given - 准备包含充电器的设备列表
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> devicesWithCharger = createDevicesWithCharger();
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(devicesWithCharger);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果（应该是T1用户，因为充电器被过滤）
            assertNotNull(result, "过滤充电器后的推荐结果不能为空");
            ExploreServiceTestUtils.verifyT1UserRecommendation(result, true);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }
    }

    /**
     * 设备缓存管理集成测试
     */
    @Nested
    @DisplayName("设备缓存管理集成测试")
    class DeviceCacheIntegrationTests {

        @Test
        @DisplayName("缓存更新逻辑 - 设备信息不完整时应调用远程服务并更新缓存")
        void testGetExploreHomeData_UpdateIncompleteCache() {
            // Given - 准备缓存信息不完整的设备列表
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> incompleteDevices = createDevicesWithoutCategoryInfo();
            List<DeviceListInfoRpcVo> completeDeviceInfo = createDeviceListInfoRpcVos();

            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(incompleteDevices);
            when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                    .thenReturn(completeDeviceInfo);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "缓存更新后的推荐结果不能为空");
            ExploreServiceTestUtils.verifyExploreHomeDataStructure(result);

            // Then - 验证远程服务调用
            verify(remoteDeviceManageService, times(1)).listInfoByDeviceId(anyList());

            // Then - 验证Redis缓存更新操作
            redisUtilsMock.verify(() -> RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId), times(1));
            redisUtilsMock.verify(
                    () -> RedisUtils.setCacheList(eq(RedisConstant.USER_BIND_DEVICES + userId), anyList()), times(1));
        }

        @Test
        @DisplayName("缓存删除操作 - 缓存更新时应先删除旧缓存")
        void testGetExploreHomeData_CacheDeleteOperation() {
            // Given - 准备需要更新缓存的场景
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> incompleteDevices = createDevicesWithoutCategoryInfo();
            List<DeviceListInfoRpcVo> completeDeviceInfo = createDeviceListInfoRpcVos();

            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(incompleteDevices);
            when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                    .thenReturn(completeDeviceInfo);

            // When - 执行被测试方法
            exploreService.getExploreHomeData(userId);

            // Then - 验证缓存删除操作被调用
            redisUtilsMock.verify(() -> RedisUtils.deleteObject(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("缓存设置操作 - 缓存更新时应设置新的缓存数据")
        void testGetExploreHomeData_CacheSetOperation() {
            // Given - 准备需要更新缓存的场景
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> incompleteDevices = createDevicesWithoutCategoryInfo();
            List<DeviceListInfoRpcVo> completeDeviceInfo = createDeviceListInfoRpcVos();

            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(incompleteDevices);
            when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                    .thenReturn(completeDeviceInfo);

            // When - 执行被测试方法
            exploreService.getExploreHomeData(userId);

            // Then - 验证缓存设置操作被调用
            redisUtilsMock.verify(
                    () -> RedisUtils.setCacheList(eq(RedisConstant.USER_BIND_DEVICES + userId), anyList()), times(1));
        }

        @Test
        @DisplayName("缓存完整性检查 - 设备分类信息完整时不应调用远程服务")
        void testGetExploreHomeData_CompleteCache_NoRemoteCall() {
            // Given - 准备完整的缓存信息
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> completeDevices = createT2UserDevices();
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(completeDevices);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "完整缓存的推荐结果不能为空");
            ExploreServiceTestUtils.verifyT2UserRecommendation(result, true, true);

            // Then - 验证不应该调用远程服务
            verify(remoteDeviceManageService, never()).listInfoByDeviceId(anyList());

            // Then - 验证不应该更新缓存
            redisUtilsMock.verify(() -> RedisUtils.deleteObject(anyString()), never());
            redisUtilsMock.verify(() -> RedisUtils.setCacheList(anyString(), anyList()), never());
        }
    }

    /**
     * Redis异常处理集成测试
     */
    @Nested
    @DisplayName("Redis异常处理集成测试")
    class RedisExceptionHandlingTests {

        @Test
        @DisplayName("Redis异常处理 - Redis获取缓存异常时应降级为T0用户")
        void testGetExploreHomeData_RedisException_FallbackToT0() {
            // Given - 准备Redis异常场景
            Long userId = TEST_USER_ID;
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenThrow(new RuntimeException("Redis connection failed"));

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果（应该降级为T0用户推荐）
            assertNotNull(result, "Redis异常时推荐结果不能为空");
            ExploreServiceTestUtils.verifyT0UserRecommendation(result);

            // Then - 验证Redis调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("Redis缓存设置异常 - 缓存设置失败不应影响业务逻辑")
        void testGetExploreHomeData_RedisCacheSetException() {
            // Given - 准备缓存设置异常场景
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> incompleteDevices = createDevicesWithoutCategoryInfo();
            List<DeviceListInfoRpcVo> completeDeviceInfo = createDeviceListInfoRpcVos();

            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(incompleteDevices);
            redisUtilsMock.when(() -> RedisUtils.setCacheList(anyString(), anyList()))
                    .thenThrow(new RuntimeException("Redis set failed"));
            when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                    .thenReturn(completeDeviceInfo);

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果（业务逻辑不应受影响）
            assertNotNull(result, "缓存设置异常时推荐结果不能为空");
            ExploreServiceTestUtils.verifyExploreHomeDataStructure(result);

            // Then - 验证远程服务仍被调用
            verify(remoteDeviceManageService, times(1)).listInfoByDeviceId(anyList());
        }
    }

    /**
     * 边界条件集成测试
     */
    @Nested
    @DisplayName("边界条件集成测试")
    class BoundaryConditionIntegrationTests {

        @Test
        @DisplayName("边界条件 - 用户ID为null时的处理")
        void testGetExploreHomeData_NullUserId() {
            // Given - 准备用户ID为null的场景
            Long userId = null;
            redisUtilsMock.when(() -> RedisUtils.getCacheList(anyString()))
                    .thenReturn(Collections.emptyList());

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果（应该降级为T0用户推荐）
            assertNotNull(result, "用户ID为null时推荐结果不能为空");
            ExploreServiceTestUtils.verifyT0UserRecommendation(result);
        }

        @Test
        @DisplayName("边界条件 - 设备列表为空集合时的处理")
        void testGetExploreHomeData_EmptyDeviceList() {
            // Given - 准备设备列表为空集合的场景
            Long userId = TEST_USER_ID;
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(Collections.emptyList());

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果（应该是T0用户推荐）
            assertNotNull(result, "设备列表为空时推荐结果不能为空");
            ExploreServiceTestUtils.verifyT0UserRecommendation(result);

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        @Test
        @DisplayName("边界条件 - 远程服务返回空列表时的处理")
        void testGetExploreHomeData_RemoteServiceEmptyResponse() {
            // Given - 准备远程服务返回空列表的场景
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> incompleteDevices = createDevicesWithoutCategoryInfo();

            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(incompleteDevices);
            when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                    .thenReturn(Collections.emptyList());

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            // Then - 验证结果
            assertNotNull(result, "远程服务返回空列表时推荐结果不能为空");
            ExploreServiceTestUtils.verifyExploreHomeDataStructure(result);

            // Then - 验证远程服务调用
            verify(remoteDeviceManageService, times(1)).listInfoByDeviceId(anyList());
        }
    }

    /**
     * 性能和并发集成测试
     */
    @Nested
    @DisplayName("性能和并发集成测试")
    class PerformanceAndConcurrencyTests {

        @Test
        @DisplayName("性能测试 - 大量设备列表的处理性能")
        void testGetExploreHomeData_LargeDeviceList_Performance() {
            // Given - 准备大量设备的场景
            Long userId = TEST_USER_ID;
            List<DeviceShortInfo> largeDeviceList = createLargeDeviceList(100);
            redisUtilsMock.when(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId))
                    .thenReturn(largeDeviceList);

            long startTime = System.currentTimeMillis();

            // When - 执行被测试方法
            ExploreBannerDto result = exploreService.getExploreHomeData(userId);

            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // Then - 验证结果和性能
            assertNotNull(result, "大量设备列表处理结果不能为空");
            ExploreServiceTestUtils.verifyExploreHomeDataStructure(result);
            assertTrue(executionTime < 5000, "处理大量设备列表的时间应该在5秒内，实际时间: " + executionTime + "ms");

            // Then - 验证Redis缓存调用
            redisUtilsMock.verify(() -> RedisUtils.getCacheList(RedisConstant.USER_BIND_DEVICES + userId), times(1));
        }

        /**
         * 创建大量设备列表用于性能测试
         */
        private List<DeviceShortInfo> createLargeDeviceList(int count) {
            List<DeviceShortInfo> devices = new java.util.ArrayList<>();
            String[] categories = { MOWERS_CATEGORY, BLOWERS_CATEGORY, STRING_TRIMMERS_CATEGORY, LIFESTYLE_CATEGORY };

            for (int i = 0; i < count; i++) {
                String category = categories[i % categories.length];
                devices.add(createDeviceShortInfo("device" + i, category, "MODEL" + i, "Model " + i));
            }
            return devices;
        }
    }
}