package com.chervon.iot.app.service;

import com.chervon.iot.app.config.DeviceUsageHisProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/9/19 14:48
 * @desc 描述
 */
@SpringBootTest
public class UsageHistoryTest {

    @Autowired
    DeviceUsageHisProperties deviceUsageHisProperties;

    @Test
    public void test(){
        System.out.println(deviceUsageHisProperties.getProduct().get(0));
    }
}
