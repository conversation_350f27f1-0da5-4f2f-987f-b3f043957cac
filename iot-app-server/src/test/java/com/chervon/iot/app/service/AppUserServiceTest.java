package com.chervon.iot.app.service;

import cn.dev33.satoken.stp.StpUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.domain.vo.device.AppDeviceVo;
import com.chervon.usercenter.api.vo.UserVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.Locale;

import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description
 */
@SpringBootTest
public class AppUserServiceTest {
    @Autowired
    private AppUserService appUserService;
    @Autowired
    private AppDeviceService appDeviceService;


    @Test
    void deleteByEmail() {
        String email = "<EMAIL>";
        try {
            appUserService.deleteByEmail(email);
        } catch (Exception e) {
            // 记录异常信息
            fail("Test failed with exception: " + e.getMessage());
        }
    }

    @Test
    void testDeviceList() {
        String language = "en";
        Long userId = 1798287395820490754L;
        try {
            LocaleContextHolder.setLocale(new Locale(language));
            StpUtil.login(userId);
            String tokenValue = StpUtil.getTokenValue();
            UserVo appUserVo = new UserVo();
            appUserVo.setId(userId);
            appUserVo.setEmail("<EMAIL>");
            appUserVo.setFirstName("first");
            appUserVo.setLastName("last");
            RedisUtils.setCacheObject(tokenValue, appUserVo);
            List<AppDeviceVo> appDeviceVos = appDeviceService.listBoundDevices(language, "1.2.3");
        } catch (Exception e) {
            // 记录异常信息
            fail( e.getMessage());
        }

    }
}
