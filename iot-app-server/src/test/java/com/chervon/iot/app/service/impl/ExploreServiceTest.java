package com.chervon.iot.app.service.impl;

import com.chervon.common.core.domain.PageResult;
import com.chervon.iot.app.domain.dto.explore.ExploreBannerDto;
import com.chervon.iot.app.domain.dto.explore.HaveFunDto;
import com.chervon.iot.app.domain.vo.explore.HaveFunReq;
import com.chervon.iot.app.service.ExploreService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class ExploreServiceTest {

    @Autowired
    ExploreService exploreService;

    @Test
    public void testGetExploreHomeData() {
        String userId = "1759834116578783234";
        ExploreBannerDto exploreBannerDto = exploreService.getExploreHomeData(Long.parseLong(userId));
        Assertions.assertNotNull(exploreBannerDto);
        System.out.println(exploreBannerDto);
    }

    @Test
    public void testGetHaveFunData() {
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(1);
        req.setPageSize(100);
        PageResult<HaveFunDto> exploreBannerDto = exploreService.getHaveFunContents(req);
        Assertions.assertNotNull(exploreBannerDto);
        System.out.println(exploreBannerDto);
    }
}
