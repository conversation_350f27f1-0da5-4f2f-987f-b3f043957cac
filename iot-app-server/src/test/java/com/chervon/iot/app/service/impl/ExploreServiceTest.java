package com.chervon.iot.app.service.impl;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.exception.AppException;
import com.chervon.iot.app.config.ExploreProperties;
import com.chervon.iot.app.domain.dataobject.promotion.PromotionProduct;
import com.chervon.iot.app.domain.dto.device.DeviceShortInfo;
import com.chervon.iot.app.domain.dto.explore.*;
import com.chervon.iot.app.domain.vo.explore.HaveFunReq;
import com.chervon.iot.app.service.HaveFunService;
import com.chervon.iot.app.service.PromotionProductService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.DeviceListInfoRpcVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExploreServiceImpl 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExploreService 单元测试")
class ExploreServiceTest {

    @Mock
    private PromotionProductService promotionProductService;

    @Mock
    private HaveFunService haveFunService;

    @Mock
    private RemoteDeviceManageService remoteDeviceManageService;

    @Mock
    private ExploreProperties exploreProperties;

    @InjectMocks
    private ExploreServiceImpl exploreService;

    private static final Long TEST_USER_ID = 123L;
    private static final String MOWERS_CATEGORY = "Mowers";
    private static final String BLOWERS_CATEGORY = "Blowers";
    private static final String STRING_TRIMMERS_CATEGORY = "String Trimmers";
    private static final String LIFESTYLE_CATEGORY = "Lifestyle";
    private static final String BATTERY_CHARGER_CATEGORY = "Batteries & Chargers";

    @BeforeEach
    void setUp() {
        // 设置默认的推荐产品数据
        setupDefaultPromotionProducts();
        // 设置默认的新品配置
        setupDefaultNewProductConfig();
    }

    // ==================== getExploreHomeData 测试 ====================

    @Test
    @DisplayName("测试统一推荐Banner - unified=1且配置完整")
    void testGetExploreHomeData_UnifiedBanner_Success() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("1");
        Map<String, Object> unifiedBanner = new HashMap<>();
        unifiedBanner.put("1", "Mowers|LM2206SP");
        unifiedBanner.put("2", "String Trimmers|ST1611T");
        unifiedBanner.put("3", "Blowers|LB6703");
        when(exploreProperties.getUnifiedBanner()).thenReturn(unifiedBanner);

        // When
        ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

        // Then
        assertNotNull(result);
        assertNotNull(result.getPromotionProduct1());
        assertNotNull(result.getPromotionProduct2());
        assertNotNull(result.getNewProduct());

        verify(promotionProductService, times(3)).selectByPromotionProductCategory(anyString());
        verify(exploreProperties).getUnified();
        verify(exploreProperties).getUnifiedBanner();
    }

    @Test
    @DisplayName("测试统一推荐Banner - unified=1但配置为空")
    void testGetExploreHomeData_UnifiedBanner_EmptyConfig() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("1");
        when(exploreProperties.getUnifiedBanner()).thenReturn(new HashMap<>());

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList(anyString())).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            // 应该走T0用户逻辑
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
        }
    }

    @Test
    @DisplayName("测试T0用户推荐逻辑 - 无设备用户")
    void testGetExploreHomeData_T0User_NoDevices() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList(anyString())).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
            assertNotNull(result.getNewProduct());

            // 验证调用了正确的推荐产品类别
            verify(promotionProductService).selectByPromotionProductCategory(MOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(BLOWERS_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试T1用户推荐逻辑 - 有Mowers设备")
    void testGetExploreHomeData_T1User_WithMowers() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        List<DeviceShortInfo> deviceList = createDeviceList(MOWERS_CATEGORY);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList(anyString())).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());

            // T1用户有Mowers，应该推荐Blowers和String Trimmers
            verify(promotionProductService).selectByPromotionProductCategory(BLOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY);
        }
    }
