package com.chervon.iot.app.service.impl;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.iot.app.api.exception.AppErrorCode;
import com.chervon.iot.app.api.exception.AppException;
import com.chervon.iot.app.config.ExploreProperties;
import com.chervon.iot.app.domain.dataobject.promotion.PromotionProduct;
import com.chervon.iot.app.domain.dto.device.DeviceShortInfo;
import com.chervon.iot.app.domain.dto.explore.*;
import com.chervon.iot.app.domain.vo.explore.HaveFunReq;
import com.chervon.iot.app.service.HaveFunService;
import com.chervon.iot.app.service.PromotionProductService;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.DeviceListInfoRpcVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ExploreServiceImpl 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExploreService 单元测试")
class ExploreServiceTest {

    @Mock
    private PromotionProductService promotionProductService;

    @Mock
    private HaveFunService haveFunService;

    @Mock
    private RemoteDeviceManageService remoteDeviceManageService;

    @Mock
    private ExploreProperties exploreProperties;

    @InjectMocks
    private ExploreServiceImpl exploreService;

    private static final Long TEST_USER_ID = 123L;
    private static final String MOWERS_CATEGORY = "Mowers";
    private static final String BLOWERS_CATEGORY = "Blowers";
    private static final String STRING_TRIMMERS_CATEGORY = "String Trimmers";
    private static final String LIFESTYLE_CATEGORY = "Lifestyle";
    private static final String BATTERY_CHARGER_CATEGORY = "Batteries & Chargers";

    @BeforeEach
    void setUp() {
        // 设置默认的推荐产品数据
        setupDefaultPromotionProducts();
        // 设置默认的新品配置
        setupDefaultNewProductConfig();
    }

    // ==================== getExploreHomeData 测试 ====================

    @Test
    @DisplayName("测试统一推荐Banner - unified=1且配置完整")
    void testGetExploreHomeData_UnifiedBanner_Success() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("1");
        Map<String, Object> unifiedBanner = new HashMap<>();
        unifiedBanner.put("1", "Mowers|LM2206SP");
        unifiedBanner.put("2", "String Trimmers|ST1611T");
        unifiedBanner.put("3", "Blowers|LB6703");
        when(exploreProperties.getUnifiedBanner()).thenReturn(unifiedBanner);

        // When
        ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

        // Then
        assertNotNull(result);
        assertNotNull(result.getPromotionProduct1());
        assertNotNull(result.getPromotionProduct2());
        assertNotNull(result.getNewProduct());

        verify(promotionProductService, times(3)).selectByPromotionProductCategory(anyString());
        verify(exploreProperties).getUnified();
        verify(exploreProperties).getUnifiedBanner();
    }

    @Test
    @DisplayName("测试统一推荐Banner - unified=1但配置为空")
    void testGetExploreHomeData_UnifiedBanner_EmptyConfig() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("1");
        when(exploreProperties.getUnifiedBanner()).thenReturn(new HashMap<>());

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            // 应该走T0用户逻辑
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
        }
    }

    @Test
    @DisplayName("测试T0用户推荐逻辑 - 无设备用户")
    void testGetExploreHomeData_T0User_NoDevices() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
            assertNotNull(result.getNewProduct());

            // 验证调用了正确的推荐产品类别
            verify(promotionProductService).selectByPromotionProductCategory(MOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(BLOWERS_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试T1用户推荐逻辑 - 有Mowers设备")
    void testGetExploreHomeData_T1User_WithMowers() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        List<DeviceShortInfo> deviceList = createDeviceList(MOWERS_CATEGORY);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());

            // T1用户有Mowers，应该推荐Blowers和String Trimmers
            verify(promotionProductService).selectByPromotionProductCategory(BLOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试T1用户推荐逻辑 - 无Mowers设备")
    void testGetExploreHomeData_T1User_WithoutMowers() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        List<DeviceShortInfo> deviceList = createDeviceList(BLOWERS_CATEGORY);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());

            // T1用户无Mowers，应该推荐Mowers和String Trimmers
            verify(promotionProductService).selectByPromotionProductCategory(MOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试T2用户推荐逻辑 - 有Mowers和Blowers")
    void testGetExploreHomeData_T2User_WithMowersAndBlowers() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        List<DeviceShortInfo> deviceList = createDeviceList(MOWERS_CATEGORY, BLOWERS_CATEGORY);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());

            // T2用户有Mowers和Blowers，应该推荐String Trimmers和Lifestyle
            verify(promotionProductService).selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(LIFESTYLE_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试T3用户推荐逻辑 - 有Mowers、Blowers和String Trimmers")
    void testGetExploreHomeData_T3User_WithAllCategories() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        List<DeviceShortInfo> deviceList = createDeviceList(MOWERS_CATEGORY, BLOWERS_CATEGORY, STRING_TRIMMERS_CATEGORY);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());

            // T3用户有所有主要类别，应该推荐Lifestyle产品
            verify(promotionProductService, times(2)).selectByPromotionProductCategory(LIFESTYLE_CATEGORY);
        }
    }

    @Test
    @DisplayName("测试缓存更新场景 - 缓存数据不完整需要更新")
    void testGetExploreHomeData_CacheUpdate_IncompleteData() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        // 创建缺少分类信息的设备列表
        List<DeviceShortInfo> incompleteDeviceList = new ArrayList<>();
        DeviceShortInfo device = new DeviceShortInfo();
        device.setDeviceId("device1");
        device.setCategoryName(null); // 缺少分类信息
        incompleteDeviceList.add(device);

        // 创建完整的设备信息
        List<DeviceListInfoRpcVo> completeDeviceList = new ArrayList<>();
        DeviceListInfoRpcVo deviceRpc = new DeviceListInfoRpcVo();
        deviceRpc.setDeviceId("device1");
        deviceRpc.setCategoryName(MOWERS_CATEGORY);
        deviceRpc.setCommodityModel("LM2206SP");
        deviceRpc.setModel("Model1");
        deviceRpc.setCategoryId("cat1");
        completeDeviceList.add(deviceRpc);

        when(remoteDeviceManageService.listInfoByDeviceId(anyList())).thenReturn(completeDeviceList);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(incompleteDeviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);

            // 验证缓存被更新
            redisUtilsMock.verify(() -> RedisUtils.deleteObject("app:device:bind:" + TEST_USER_ID));
            redisUtilsMock.verify(() -> RedisUtils.setCacheList(eq("app:device:bind:" + TEST_USER_ID), anyList()));

            // 验证远程服务被调用
            verify(remoteDeviceManageService).listInfoByDeviceId(anyList());
        }
    }

    @Test
    @DisplayName("测试推荐产品配置缺失异常")
    void testGetExploreHomeData_PromotionProductNotFound_ThrowsException() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        when(promotionProductService.selectByPromotionProductCategory(anyString()))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(null);

            // When & Then
            AppException exception = assertThrows(AppException.class,
                () -> exploreService.getExploreHomeData(TEST_USER_ID));

            assertEquals(AppErrorCode.DEFAULT_PROMOTION_NOT_SET.getCode(), exception.getCode());
        }
    }

    @Test
    @DisplayName("测试过滤电池充电器类别设备")
    void testGetExploreHomeData_FilterBatteryChargerCategory() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        // 创建包含电池充电器的设备列表
        List<DeviceShortInfo> deviceList = new ArrayList<>();
        DeviceShortInfo mowerDevice = createDevice("device1", MOWERS_CATEGORY);
        DeviceShortInfo batteryDevice = createDevice("device2", BATTERY_CHARGER_CATEGORY);
        deviceList.add(mowerDevice);
        deviceList.add(batteryDevice);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(deviceList);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            // 应该按T1用户逻辑处理（只有1个非电池充电器设备）
            verify(promotionProductService).selectByPromotionProductCategory(BLOWERS_CATEGORY);
            verify(promotionProductService).selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY);
        }
    }

    // ==================== getHaveFunContents 测试 ====================

    @Test
    @DisplayName("测试获取Have Fun内容 - 正常情况")
    void testGetHaveFunContents_Success() {
        // Given
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(1);
        req.setPageSize(10);

        PageResult<HaveFunDto> expectedResult = new PageResult<>();
        List<HaveFunDto> haveFunList = new ArrayList<>();
        HaveFunDto haveFunDto = new HaveFunDto();
        haveFunDto.setTitle("Test Title");
        haveFunDto.setText("Test Content");
        haveFunList.add(haveFunDto);
        expectedResult.setList(haveFunList);
        expectedResult.setTotal(1L);

        when(haveFunService.selectAllHaveFunContents(req)).thenReturn(expectedResult);

        // When
        PageResult<HaveFunDto> result = exploreService.getHaveFunContents(req);

        // Then
        assertNotNull(result);
        assertNotNull(result.getList());
        assertFalse(result.getList().isEmpty());
        assertEquals(1, result.getList().size());
        assertEquals("Test Title", result.getList().get(0).getTitle());

        verify(haveFunService).selectAllHaveFunContents(req);
    }

    @Test
    @DisplayName("测试获取Have Fun内容 - 服务返回null")
    void testGetHaveFunContents_ServiceReturnsNull_ThrowsException() {
        // Given
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(1);
        req.setPageSize(10);

        when(haveFunService.selectAllHaveFunContents(req)).thenReturn(null);

        // When & Then
        AppException exception = assertThrows(AppException.class,
            () -> exploreService.getHaveFunContents(req));

        assertEquals(AppErrorCode.HAVE_FUN_LIST_ERROR.getCode(), exception.getCode());
        verify(haveFunService).selectAllHaveFunContents(req);
    }

    @Test
    @DisplayName("测试获取Have Fun内容 - 服务返回空列表")
    void testGetHaveFunContents_ServiceReturnsEmptyList_ThrowsException() {
        // Given
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(1);
        req.setPageSize(10);

        PageResult<HaveFunDto> emptyResult = new PageResult<>();
        emptyResult.setList(new ArrayList<>());
        emptyResult.setTotal(0L);

        when(haveFunService.selectAllHaveFunContents(req)).thenReturn(emptyResult);

        // When & Then
        AppException exception = assertThrows(AppException.class,
            () -> exploreService.getHaveFunContents(req));

        assertEquals(AppErrorCode.HAVE_FUN_LIST_ERROR.getCode(), exception.getCode());
        verify(haveFunService).selectAllHaveFunContents(req);
    }

    @Test
    @DisplayName("测试获取Have Fun内容 - 不同分页参数")
    void testGetHaveFunContents_DifferentPagination() {
        // Given
        HaveFunReq req = new HaveFunReq();
        req.setPageNum(2);
        req.setPageSize(20);

        PageResult<HaveFunDto> expectedResult = new PageResult<>();
        List<HaveFunDto> haveFunList = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            HaveFunDto haveFunDto = new HaveFunDto();
            haveFunDto.setTitle("Title " + i);
            haveFunList.add(haveFunDto);
        }
        expectedResult.setList(haveFunList);
        expectedResult.setTotal(100L);

        when(haveFunService.selectAllHaveFunContents(req)).thenReturn(expectedResult);

        // When
        PageResult<HaveFunDto> result = exploreService.getHaveFunContents(req);

        // Then
        assertNotNull(result);
        assertEquals(20, result.getList().size());
        assertEquals(100L, result.getTotal());

        verify(haveFunService).selectAllHaveFunContents(req);
    }

    // ==================== 辅助方法 ====================

    /**
     * 设置默认的推荐产品数据
     */
    private void setupDefaultPromotionProducts() {
        // 为每个类别创建推荐产品
        when(promotionProductService.selectByPromotionProductCategory(MOWERS_CATEGORY))
                .thenReturn(createPromotionProducts(MOWERS_CATEGORY, "Mower Product"));
        when(promotionProductService.selectByPromotionProductCategory(BLOWERS_CATEGORY))
                .thenReturn(createPromotionProducts(BLOWERS_CATEGORY, "Blower Product"));
        when(promotionProductService.selectByPromotionProductCategory(STRING_TRIMMERS_CATEGORY))
                .thenReturn(createPromotionProducts(STRING_TRIMMERS_CATEGORY, "Trimmer Product"));
        when(promotionProductService.selectByPromotionProductCategory(LIFESTYLE_CATEGORY))
                .thenReturn(createPromotionProducts(LIFESTYLE_CATEGORY, "Lifestyle Product", 2));
    }

    /**
     * 设置默认的新品配置
     */
    private void setupDefaultNewProductConfig() {
        Map<String, Object> newProductConfig = new HashMap<>();
        newProductConfig.put("imageUrl", "https://example.com/new-product.jpg");
        newProductConfig.put("shopUrl", "https://example.com/shop/new-product");
        when(exploreProperties.getNewProduct()).thenReturn(newProductConfig);
    }

    /**
     * 创建推荐产品列表
     */
    private List<PromotionProduct> createPromotionProducts(String category, String productName) {
        return createPromotionProducts(category, productName, 1);
    }

    /**
     * 创建推荐产品列表
     */
    private List<PromotionProduct> createPromotionProducts(String category, String productName, int count) {
        List<PromotionProduct> products = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            PromotionProduct product = new PromotionProduct();
            product.setProductCategory(category);
            product.setProductName(productName + (count > 1 ? " " + (i + 1) : ""));
            product.setCommodityModel("MODEL" + i);
            product.setImageUrl("https://example.com/" + category.toLowerCase().replace(" ", "") + i + ".jpg");
            product.setShopUrl("https://example.com/shop/" + category.toLowerCase().replace(" ", "") + i);
            products.add(product);
        }
        return products;
    }

    /**
     * 创建设备列表
     */
    private List<DeviceShortInfo> createDeviceList(String... categories) {
        List<DeviceShortInfo> deviceList = new ArrayList<>();
        for (int i = 0; i < categories.length; i++) {
            DeviceShortInfo device = createDevice("device" + (i + 1), categories[i]);
            deviceList.add(device);
        }
        return deviceList;
    }

    /**
     * 创建单个设备信息
     */
    private DeviceShortInfo createDevice(String deviceId, String categoryName) {
        DeviceShortInfo device = new DeviceShortInfo();
        device.setDeviceId(deviceId);
        device.setCategoryName(categoryName);
        device.setCommodityModel("MODEL_" + categoryName);
        device.setModel("Model_" + categoryName);
        device.setCategoryId("cat_" + categoryName);
        device.setSn("SN_" + deviceId);
        device.setProductId(1L);
        return device;
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    @DisplayName("测试新品配置为空的情况")
    void testGetExploreHomeData_NewProductConfigEmpty() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        when(exploreProperties.getNewProduct()).thenReturn(null);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNull(result.getNewProduct()); // 新品信息应该为null
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
        }
    }

    @Test
    @DisplayName("测试新品配置部分字段为空")
    void testGetExploreHomeData_NewProductConfigPartialEmpty() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");
        Map<String, Object> partialConfig = new HashMap<>();
        partialConfig.put("imageUrl", "https://example.com/image.jpg");
        // shopUrl 缺失
        when(exploreProperties.getNewProduct()).thenReturn(partialConfig);

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertNotNull(result.getNewProduct());
            assertEquals("https://example.com/image.jpg", result.getNewProduct().getImageUrl());
            assertNull(result.getNewProduct().getShopUrl());
        }
    }

    @Test
    @DisplayName("测试统一Banner配置格式错误")
    void testGetExploreHomeData_UnifiedBannerInvalidFormat() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("1");
        Map<String, Object> invalidBanner = new HashMap<>();
        invalidBanner.put("1", "InvalidFormat"); // 缺少分隔符
        when(exploreProperties.getUnifiedBanner()).thenReturn(invalidBanner);

        // When & Then
        assertThrows(ArrayIndexOutOfBoundsException.class,
            () -> exploreService.getExploreHomeData(TEST_USER_ID));
    }

    @Test
    @DisplayName("测试用户ID为null的情况")
    void testGetExploreHomeData_NullUserId() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + null)).thenReturn(null);

            // When
            ExploreBannerDto result = exploreService.getExploreHomeData(null);

            // Then
            assertNotNull(result);
            // 应该正常处理，按T0用户逻辑
            assertNotNull(result.getPromotionProduct1());
            assertNotNull(result.getPromotionProduct2());
        }
    }

    @Test
    @DisplayName("测试远程设备服务调用失败")
    void testGetExploreHomeData_RemoteServiceFailure() {
        // Given
        when(exploreProperties.getUnified()).thenReturn("0");

        List<DeviceShortInfo> incompleteDeviceList = new ArrayList<>();
        DeviceShortInfo device = new DeviceShortInfo();
        device.setDeviceId("device1");
        device.setCategoryName(null);
        incompleteDeviceList.add(device);

        when(remoteDeviceManageService.listInfoByDeviceId(anyList()))
                .thenThrow(new RuntimeException("Remote service error"));

        try (MockedStatic<RedisUtils> redisUtilsMock = mockStatic(RedisUtils.class)) {
            redisUtilsMock.when(() -> RedisUtils.getCacheList("app:device:bind:" + TEST_USER_ID)).thenReturn(incompleteDeviceList);

            // When & Then
            assertThrows(RuntimeException.class,
                () -> exploreService.getExploreHomeData(TEST_USER_ID));
        }
    }

    @Test
    @DisplayName("测试Have Fun请求参数为null")
    void testGetHaveFunContents_NullRequest() {
        // Given
        when(haveFunService.selectAllHaveFunContents(null)).thenReturn(null);

        // When & Then
        AppException exception = assertThrows(AppException.class,
            () -> exploreService.getHaveFunContents(null));

        assertEquals(AppErrorCode.HAVE_FUN_LIST_ERROR.getCode(), exception.getCode());
    }
}
