package com.chervon.common.log.logback.conversion;

import java.util.Stack;

/**
 * 
 * @date 2023/5/16 14:08
 */
public class ClassicConverterContextHolder {
    private static final ThreadLocal<Stack<ClassicConverterContext>> CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL = new ThreadLocal<>();

    public static ClassicConverterContext get() {
        Stack<ClassicConverterContext> stack = CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL.get();
        if (stack == null) {
            return null;
        }
        if (stack.size() > 0) {
            return stack.peek();
        }
        return null;
    }

    public static void put(ClassicConverterContext context) {
        Stack<ClassicConverterContext> stack = CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL.get();
        if (null == stack) {
            stack = new Stack<>();
            CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL.set(stack);
        }
        stack.push(context);
    }

    public static void remove() {
        Stack<ClassicConverterContext> stack = CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL.get();
        if (null != stack) {
            stack.pop();
            if (stack.size() == 0) {
                CLASSIC_CONVERTER_CONTEXT_THREAD_LOCAL.remove();
            }
        }
    }
}
