package com.chervon.authority.api.core;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/12/27 15:15
 */
@Data
public class SysLogDto implements Serializable {

    /**
     * 用户Id(内部用户)
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 浏览器类型
     */
    private String browser;
    /**
     * ip
     */
    private String ip;
    /**
     * 操作类型
     */
    private String operation;
    /**
     * 操作模块
     */
    private String operationModel;
    /**
     * 入参
     */
    private String input;
    /**
     * 出参
     */
    private String output;
    /**
     * 操作结果: 0失败 1成功
     */
    private Integer operationResult;
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
}
