package com.chervon.operation.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/15 17:09
 */
@Data
@ApiModel(description = "产品百科对象")
public class ProductWikiVo implements Serializable {

    @ApiModelProperty(value = "产品属性")
    private ProductVo product = new ProductVo();

    @ApiModelProperty(value = "用户手册属性")
    private FileVo manual = new FileVo();

    @ApiModelProperty(value = "操作指导，即how-to videos")
    private List<FileVo> guidance = new ArrayList<>();

    @ApiModelProperty(value = "faq")
    private List<FaqVo> faq = new ArrayList<>();

}
