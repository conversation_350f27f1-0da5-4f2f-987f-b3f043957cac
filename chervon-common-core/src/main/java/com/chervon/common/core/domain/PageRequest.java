package com.chervon.common.core.domain;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * <AUTHOR>
 * @description: 分页参数基础类
 * @create: 2019-09-25 09:24
 **/
@Data
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int DEFAULT_ZONE = 8;

    /**
     * 页码
     */
    @NotNull
    @Min(value = 1)
    private Integer pageNum = 1;

    private Integer zone;

    public int getZone() {
        return this.zone == null ? DEFAULT_ZONE : this.zone;
    }

    /**
     * 每页显示数量
     */
    @NotNull
    @Min(value = 1)
    private Integer pageSize = 1000;

    /**
     * 排序
     */
    private Integer orderBy;

    /**
     * 排序规则 0:升序1:降序
     */
    private Integer orderType;

    private String createStartTime;

    private String createEndTime;

    private String updateStartTime;

    private String updateEndTime;

    private String createBy;

    private String updateBy;

    private String applyStartTime;

    private String applyEndTime;

    private String approveStartTime;

    private String approveEndTime;

    public String getCreateStartTime() {
        if (StringUtils.isNotBlank(this.createStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.createStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getUpdateStartTime() {
        if (StringUtils.isNotBlank(this.updateStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.updateStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getCreateEndTime() {
        if (StringUtils.isNotBlank(this.createEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.createEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getUpdateEndTime() {
        if (StringUtils.isNotBlank(this.updateEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.updateEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getApplyStartTime() {
        if (StringUtils.isNotBlank(this.applyStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.applyStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getApproveStartTime() {
        if (StringUtils.isNotBlank(this.approveStartTime)) {
            LocalDateTime time = LocalDateTime.parse(this.approveStartTime + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getApplyEndTime() {
        if (StringUtils.isNotBlank(this.applyEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.applyEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

    public String getApproveEndTime() {
        if (StringUtils.isNotBlank(this.approveEndTime)) {
            LocalDateTime time = LocalDateTime.parse(this.approveEndTime + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return time.minusHours(this.getZone()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return null;
    }

}
