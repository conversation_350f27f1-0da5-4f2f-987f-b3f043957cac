package com.chervon.common.core.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/14 21:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListRpcReq<T> extends RpcBase implements Serializable {

    private List<T> data;

    public ListRpcReq() {
        super();
    }

    public ListRpcReq(List<T> data) {
        super();
        this.data = data;
    }

    public ListRpcReq(List<T> data, String lang) {
        super(lang);
        this.data = data;
    }

    public ListRpcReq(List<T> data, String lang, LoginSysUser login) {
        super(lang, login);
        this.data = data;
    }

    public ListRpcReq(List<T> data, LoginSysUser login) {
        super(login);
        this.data = data;
    }

}
