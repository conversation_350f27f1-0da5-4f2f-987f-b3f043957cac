package com.chervon.configuration.config;

import com.chervon.authority.sdk.config.PermissionCheckProperties;
import com.chervon.authority.sdk.interceptor.PermissionInterceptor;
import com.chervon.common.security.config.CheckPermissionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 权限安全配置
 *
 * <AUTHOR> Li
 */
@Configuration
public class PermissionConfiguration implements WebMvcConfigurer {

    @Bean
    public CheckPermissionProperties getCheckPermissionProperties() {
        return new CheckPermissionProperties();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration interceptorRegistration = registry
                .addInterceptor(new PermissionInterceptor(getPermissionCheckProperties()))
                .order(20).addPathPatterns("/**");
        interceptorRegistration.excludePathPatterns("/sso/**");
        if (getCheckPermissionProperties().getExcludeUrls() != null) {
            interceptorRegistration.excludePathPatterns(getCheckPermissionProperties().getExcludeUrls());
        }
    }

    private PermissionCheckProperties getPermissionCheckProperties() {
        CheckPermissionProperties checkPermissionProperties = getCheckPermissionProperties();
        PermissionCheckProperties permissionCheckProperties = new PermissionCheckProperties();
        permissionCheckProperties.setPermissionCheckUrl(checkPermissionProperties.getUrl());
        permissionCheckProperties.setExcludeUrls(checkPermissionProperties.getExcludeUrls());
        permissionCheckProperties.setPermissionKeyTtl(checkPermissionProperties.getPermissionKeyTtl());
        permissionCheckProperties.setEnable(checkPermissionProperties.isEnable());
        permissionCheckProperties.setAppId(checkPermissionProperties.getAppId());
        return permissionCheckProperties;
    }

}
