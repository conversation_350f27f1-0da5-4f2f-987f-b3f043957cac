package com.chervon.configuration.config;

import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.service.DictService;
import com.chervon.configuration.service.MultiLanguageContentService;
import com.chervon.configuration.service.MultiLanguageService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/8/2 10:51
 */
@Slf4j
@Component
@Order(value = 999)
public class MultiLanguageInit implements CommandLineRunner {

    @Autowired
    private MultiLanguageService multiLanguageService;

    @Autowired
    private MultiLanguageContentService multiLanguageContentService;

    @Autowired
    private DictService dictService;

    @Override
    public void run(String... args) {
        String keyLock = "multi-language:start-init";
        RLock lock = RedisUtils.getClient().getLock(keyLock);

        try {
            boolean isLocked = lock.tryLock(0, 5, TimeUnit.MINUTES);
            if (isLocked) {
                try {
                    handle();
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("start init multiLanguage do not get lock");
            }
        } catch (InterruptedException e) {
            log.error("Error occurred while trying to acquire or execute lock: {}", keyLock, e);
            Thread.currentThread().interrupt();
        }
    }




    private void handle() {
        log.info("init multiLanguage redis start...");
        multiLanguageContentService.cachePageContent(null);
        log.info("init multiLanguage redis end...");
    }
}
