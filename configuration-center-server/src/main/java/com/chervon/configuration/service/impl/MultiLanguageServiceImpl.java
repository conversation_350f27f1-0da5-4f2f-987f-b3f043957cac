package com.chervon.configuration.service.impl;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.excel.EasyExcel;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.enums.ApplicationEnum;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.common.core.utils.DateTimeZoneUtil;
import com.chervon.common.core.utils.SnowFlake;
import com.chervon.common.mybatis.config.BaseDo;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.configuration.api.core.MultiLanguageBo;
import com.chervon.configuration.api.core.MultiLanguageCreateDto;
import com.chervon.configuration.api.core.MultiLanguageNode;
import com.chervon.configuration.api.core.MultiLanguageUpdateDto;
import com.chervon.configuration.api.exception.ConfigurationErrorCode;
import com.chervon.configuration.config.ExceptionMessageUtil;
import com.chervon.configuration.entity.MultiLanguage;
import com.chervon.configuration.entity.MultiLanguageContent;
import com.chervon.configuration.mapper.MultiLanguageMapper;
import com.chervon.configuration.req.MultiLanguagePageDto;
import com.chervon.configuration.resp.*;
import com.chervon.configuration.service.DictService;
import com.chervon.configuration.service.MultiLanguageContentService;
import com.chervon.configuration.service.MultiLanguageService;
import com.chervon.configuration.util.PropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chervon.common.core.constant.CommonConstant.TEXT_AREA_SIZE;
import static com.chervon.common.core.constant.CommonConstant.TEXT_SIZE;
import static com.chervon.configuration.config.Constant.*;

/**
 * <AUTHOR>
 * @date 2022/7/10 20:35
 */
@Service
@Slf4j
public class MultiLanguageServiceImpl extends ServiceImpl<MultiLanguageMapper, MultiLanguage> implements MultiLanguageService {

    private final NacosConfigProperties nacosConfigProperties;

    @Autowired
    private MultiLanguageContentService multiLanguageContentService;

    @Autowired
    @Lazy
    private DictService dictService;

    public MultiLanguageServiceImpl(NacosConfigProperties nacosConfigProperties) {
        this.nacosConfigProperties = nacosConfigProperties;
    }

    private void handelContent(MultiLanguage language, List<MultiLanguageNode> nodes) {
        // 多语言更新到nacos
//        nacosUpdate(req.getLangCode(), ApplicationEnum.fromName(req.getSysCode()).getName(), req.getNodes());
        // 保存多语言内容
        if (CollectionUtils.isEmpty(nodes)) {
            multiLanguageContentService.remove(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangCode, language.getLangCode()));
        } else {
            List<MultiLanguageContent> contents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangCode, language.getLangCode()));
            Map<String, MultiLanguageContent> langMap = contents.stream().collect(Collectors.toMap(MultiLanguageContent::getLang, Function.identity()));
            List<MultiLanguageContent> add = new ArrayList<>();
            List<MultiLanguageContent> update = new ArrayList<>();
            for (MultiLanguageNode e : nodes) {
                if (e.getContent() == null) {
                    continue;
                }
                MultiLanguageContent mc = langMap.get(e.getLangClassCode());
                if (mc == null) {
                    MultiLanguageContent addOne = new MultiLanguageContent();
                    addOne.setLang(e.getLangClassCode());
                    addOne.setLangId(language.getId());
                    addOne.setLangCode(language.getLangCode());
                    addOne.setContent(e.getContent());
                    addOne.setShortContent(e.getContent().length() <= 20 ? e.getContent() : e.getContent().substring(0, 20) + "...");
                    add.add(addOne);
                } else {
                    MultiLanguageContent updateOne = new MultiLanguageContent();
                    updateOne.setId(mc.getId());
                    updateOne.setContent(e.getContent());
                    updateOne.setShortContent(e.getContent().length() <= 20 ? e.getContent() : e.getContent().substring(0, 20) + "...");
                    update.add(updateOne);
                    langMap.remove(e.getLangClassCode());
                }
            }
            if (!CollectionUtils.isEmpty(add)) {
                multiLanguageContentService.saveBatch(add);
            }
            if (!CollectionUtils.isEmpty(update)) {
                multiLanguageContentService.updateBatchById(update);
            }
            if (!langMap.isEmpty()) {
                multiLanguageContentService.removeBatchByIds(langMap.values().stream().map(BaseDo::getId).collect(Collectors.toList()));
            }
        }

        // 设置redis
        List<MultiLanguageContent> contents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangCode, language.getLangCode()));
        boolean f;
        try {
            Long.parseLong(language.getLangCode());
            f = true;
        } catch (Exception ex) {
            f = false;
        }
        if (!f) {
            // 非数字，页面元素
            // 按语言存入redis
            contents.forEach(i -> RedisUtils.setCacheMapValue("multiLanguage:page:" + language.getSysCode() + ":" + i.getLang(), i.getLangCode(), i.getContent()));
        }
        contents.forEach(i -> RedisUtils.setCacheMapValue(i.getLangCode(), i.getLang(), i.getContent()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createMultiLanguage(MultiLanguageCreateDto req) {
        // 判断多语言code是否重复
        MultiLanguage one = this.getOne(new LambdaQueryWrapper<MultiLanguage>().eq(MultiLanguage::getLangCode, req.getLangCode()));
        if (one != null) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_CODE_SAME, req.getLangCode());
        }
        // 保存对象
        MultiLanguage language = new MultiLanguage();
        BeanUtils.copyProperties(req, language);
        language.setIsRich(0);
        this.save(language);
        handelContent(language, req.getNodes());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMultiLanguage(MultiLanguageUpdateDto req) {
        MultiLanguage language = new MultiLanguage();
        BeanUtils.copyProperties(req, language);
        language.setId(req.getMultiLanguageId());
        language.setLangCode(null);
        this.updateById(language);
        language = this.getById(req.getMultiLanguageId());
        handelContent(language, req.getNodes());
    }

    private synchronized void nacosUpdate(String langCode, String applicationName, List<MultiLanguageNode> nodes) {
        ConfigService configService = getConfigService();
        for (MultiLanguageNode e : nodes) {
            String dataId = applicationName.toLowerCase() + "-message_" + e.getLangClassCode().toLowerCase() + ".properties";
            String content;
            try {
                content = configService.getConfig(dataId, nacosConfigProperties.getGroup(), 5000);
            } catch (NacosException ex) {
                throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_CONFIG_SERVICE_ERROR);
            }
            String newContent;
            if (!StringUtils.isEmpty(content)) {
                Properties properties = new Properties();
                try {
                    properties.load(new StringReader(content));
                } catch (IOException ex) {
                    throw ExceptionMessageUtil.getException(ConfigurationErrorCode.CONFIGURATION_IO_ERROR);
                }
                properties.put(langCode, e.getContent());
                newContent = PropertiesUtil.convertToStr(properties);
            } else {
                newContent = langCode + "=" + e.getContent();
            }
            try {
                configService.publishConfig(dataId, nacosConfigProperties.getGroup(), newContent);
            } catch (NacosException ex) {
                throw ExceptionMessageUtil.getException(ConfigurationErrorCode.CONFIGURATION_IO_ERROR);
            }
        }
    }

    private ConfigService getConfigService() {
        try {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, Objects.toString(nacosConfigProperties.getServerAddr(), ""));
            properties.put(PropertyKeyConst.NAMESPACE, Objects.toString(nacosConfigProperties.getNamespace(), ""));
            return NacosFactory.createConfigService(properties);
        } catch (Exception e) {
            throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_INIT_CONFIG_SERVICE_ERROR);
        }

    }

    @Override
    public IPage<MultiLanguagePageVo> page(MultiLanguagePageDto req) {
        if (req == null) {
            req = new MultiLanguagePageDto();
        }
        IPage<MultiLanguagePageBo> page = this.getBaseMapper().selectMultiLanguageContentPage(new Page<>(req.getPageNo(), req.getPageSize()), req);
        Page<MultiLanguagePageVo> res = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<Long> langIds = page.getRecords().stream().map(MultiLanguagePageBo::getMultiLanguageId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<MultiLanguageContent> contents = new ArrayList<>();
        if (!langIds.isEmpty()) {
            LambdaQueryWrapper<MultiLanguageContent> wrapper = new LambdaQueryWrapper<MultiLanguageContent>().in(MultiLanguageContent::getLangId, langIds);
            contents = multiLanguageContentService.list(wrapper);
        }
        Map<Long, List<MultiLanguageContent>> group = contents.stream().collect(Collectors.groupingBy(MultiLanguageContent::getLangId));
        List<MultiLanguagePageVo> records = new ArrayList<>();
        page.getRecords().forEach(e -> {
            MultiLanguagePageVo vo = new MultiLanguagePageVo();
            BeanUtils.copyProperties(e, vo);
            List<MultiLanguageContent> languageContentList = group.get(e.getMultiLanguageId());
            List<MultiLanguageNode> nodes = new ArrayList<>();
            if (!CollectionUtils.isEmpty(languageContentList)) {
                languageContentList.forEach(i -> {
                    MultiLanguageNode node = new MultiLanguageNode();
                    node.setLangClassCode(i.getLang());
                    node.setContent(i.getContent());
                    nodes.add(node);
                });
            }
            vo.setNodes(nodes);
            records.add(vo);
        });
        res.setRecords(records);
        return res;
    }

    @Override
    public MultiLanguageVo detail(Long multiLanguageId) {
        MultiLanguageVo res = new MultiLanguageVo();
        res.setMultiLanguageId(multiLanguageId);
        MultiLanguage language = this.getById(multiLanguageId);
        if (language != null) {
            BeanUtils.copyProperties(language, res);
            List<MultiLanguageNode> nodes = new ArrayList<>();
            List<MultiLanguageContent> contents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangCode, language.getLangCode()));
            contents.forEach(e -> nodes.add(new MultiLanguageNode(e.getLang(), e.getContent())));
            res.setNodes(nodes);
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, MultiLanguageBo> simpleCreateMultiLanguages(String applicationName, Map<String, String> contents) {
        StopWatch sw = new StopWatch();
        sw.start("build MultiLanguage");
        String sysCode = ApplicationEnum.fromName(applicationName).getName();
        List<MultiLanguage> data = new ArrayList<>();
        Map<String, String> contentCodeMap = new HashMap<>();
        Map<String, String> m = new HashMap<>();
        contents.forEach((k, v) -> {
            MultiLanguage language = new MultiLanguage();
            language.setLangCode(SnowFlake.nextId() + "");
            language.setSysCode(sysCode);
            data.add(language);
            contentCodeMap.put(k, language.getLangCode());
            m.put(language.getLangCode(), v);
        });
        sw.stop();
        sw.start("save MultiLanguage");
        // 批量保存对象
        this.saveBatch(data);
        sw.stop();

        // 多语言更新到nacos
//        nacosUpdateBatch(sysCode, m, LocaleContextHolder.getLocale());
        sw.start("build MultiLanguage content");
        // 配对返回
        Map<String, MultiLanguage> collect = data.stream().collect(Collectors.toMap(MultiLanguage::getLangCode, Function.identity()));
        Map<String, MultiLanguageBo> res = new HashMap<>();
        List<MultiLanguageContent> list = new ArrayList<>();
        contentCodeMap.forEach((k, v) -> {
            // k 输入的key   v  多语言code
            MultiLanguage one = collect.getOrDefault(v, new MultiLanguage());
            MultiLanguageBo bo = new MultiLanguageBo();
            BeanUtils.copyProperties(one, bo);
            bo.setLangId(one.getId());
            res.put(k, bo);

            MultiLanguageContent item = new MultiLanguageContent();
            item.setLangId(one.getId());
            item.setLangCode(one.getLangCode());
            item.setLang(LocaleContextHolder.getLocale().getLanguage());
            if (m.get(one.getLangCode()) == null) {
                item.setContent("");
                item.setShortContent("");
            } else {
                item.setContent(m.get(one.getLangCode()));
                item.setShortContent(m.get(one.getLangCode()).length() <= 20 ? m.get(one.getLangCode()) : m.get(one.getLangCode()).substring(0, 20) + "...");
            }
            list.add(item);
        });
        sw.stop();
        sw.start("save MultiLanguage content");
        multiLanguageContentService.saveBatch(list);
        sw.stop();
        sw.start("build redis");
        // 设置redis
        RBatch batch = RedisUtils.getClient().createBatch();
        list.forEach(e -> {
            boolean f;
            try {
                Long.parseLong(e.getLangCode());
                f = true;
            } catch (Exception ex) {
                f = false;
            }
            if (!f) {
                // 非数字，页面元素
                batch.getMap("multiLanguage:page:" + sysCode + ":" + e.getLang()).fastPutAsync(e.getLangCode(), e.getContent());
//                RedisUtils.setCacheMapValue("multiLanguage:page:" + sysCode + ":" + e.getLang(), e.getLangCode(), e.getContent());
            }
            batch.getMap(e.getLangCode()).fastPutAsync(e.getLang(), e.getContent());
//            RedisUtils.setCacheMapValue(e.getLangCode(), e.getLang(), e.getContent());
        });
        sw.stop();
        sw.start("excute redis");
        batch.execute();
        sw.stop();
        log.error(sw.prettyPrint());
        return res;
    }

    @Override
    public Map<String, MultiLanguageBo> simpleCreateMultiLanguagesWithLangCodes(String applicationName, Map<String, String> contents) {
        String sysCode = ApplicationEnum.fromName(applicationName).getName();
        List<MultiLanguage> data = new ArrayList<>();
        Map<String, String> contentCodeMap = new HashMap<>();
        Map<String, String> m = new HashMap<>();
        contents.forEach((k, v) -> {
            MultiLanguage language = new MultiLanguage();
            language.setLangCode(Optional.ofNullable(k).orElse(SnowFlake.nextId() + ""));
            language.setSysCode(sysCode);
            data.add(language);
            contentCodeMap.put(k, language.getLangCode());
            m.put(language.getLangCode(), v);
        });
        // 批量保存对象
        this.saveBatch(data);

        // 多语言更新到nacos
//        nacosUpdateBatch(sysCode, m, LocaleContextHolder.getLocale());

        // 配对返回
        Map<String, MultiLanguage> collect = data.stream().collect(Collectors.toMap(MultiLanguage::getLangCode, Function.identity()));
        Map<String, MultiLanguageBo> res = new HashMap<>();
        List<MultiLanguageContent> list = new ArrayList<>();
        contentCodeMap.forEach((k, v) -> {
            // k 输入的key   v  多语言code
            MultiLanguage one = collect.getOrDefault(v, new MultiLanguage());
            MultiLanguageBo bo = new MultiLanguageBo();
            BeanUtils.copyProperties(one, bo);
            bo.setLangId(one.getId());
            res.put(k, bo);

            MultiLanguageContent item = new MultiLanguageContent();
            item.setLangId(one.getId());
            item.setLangCode(one.getLangCode());
            item.setLang(LocaleContextHolder.getLocale().getLanguage());
            if (m.get(one.getLangCode()) == null) {
                item.setContent("");
                item.setShortContent("");
            } else {
                item.setContent(m.get(one.getLangCode()));
                item.setShortContent(m.get(one.getLangCode()).length() <= 20 ? m.get(one.getLangCode()) : m.get(one.getLangCode()).substring(0, 20) + "...");
            }
            list.add(item);
        });
        multiLanguageContentService.saveBatch(list);
        // 设置redis
        list.forEach(e -> {
            boolean f;
            try {
                Long.parseLong(e.getLangCode());
                f = true;
            } catch (Exception ex) {
                f = false;
            }
            if (!f) {
                // 非数字，页面元素
                RedisUtils.setCacheMapValue("multiLanguage:page:" + sysCode + ":" + e.getLang(), e.getLangCode(), e.getContent());
            }
            RedisUtils.setCacheMapValue(e.getLangCode(), e.getLang(), e.getContent());
        });
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void simpleUpdateMultiLanguages(String applicationName, Map<Long, String> langIdNewContentMap) {
        Map<String, String> codeContentMap = new HashMap<>();
        List<MultiLanguage> multiLanguages = this.listByIds(langIdNewContentMap.keySet());
        if (multiLanguages.isEmpty()) {
            return;
        }
        multiLanguages.forEach(e -> {
            if (langIdNewContentMap.get(e.getId()) != null) {
                codeContentMap.put(e.getLangCode(), langIdNewContentMap.get(e.getId()));
            } else {
                langIdNewContentMap.remove(e.getId());
            }
        });
        Map<Long, String> multiLanguagesMap = multiLanguages.stream().collect(Collectors.toMap(BaseDo::getId, MultiLanguage::getLangCode, (k1, k2) -> k2));
        List<MultiLanguageContent> addContents = new ArrayList<>();
//        nacosUpdateBatch(sysCode, codeContentMap, LocaleContextHolder.getLocale());
        langIdNewContentMap.forEach((k, v) -> {
            boolean update = multiLanguageContentService.update(new MultiLanguageContent(), new LambdaUpdateWrapper<MultiLanguageContent>()
                    .set(MultiLanguageContent::getContent, v)
                    .set(MultiLanguageContent::getShortContent, v.length() <= 20 ? v : v.substring(0, 20) + "...")
                    .eq(MultiLanguageContent::getLangId, k)
                    .eq(MultiLanguageContent::getLang, LocaleContextHolder.getLocale().getLanguage()));
            if (!update) {
                // 没有更新到数据，则新增数据
                MultiLanguageContent content = new MultiLanguageContent();
                content.setLangId(k);
                content.setLangCode(multiLanguagesMap.get(k));
                content.setLang(LocaleContextHolder.getLocale().getLanguage());
                content.setContent(v);
                content.setShortContent(v.length() <= 20 ? v : v.substring(0, 20) + "...");
                addContents.add(content);
            }
        });
        if (!addContents.isEmpty()) {
            multiLanguageContentService.saveBatch(addContents);
        }
        // 设置redis
        codeContentMap.forEach((k, v) -> {
            boolean f;
            try {
                Long.parseLong(k);
                f = true;
            } catch (Exception ex) {
                f = false;
            }
            if (!f) {
                // 非数字，页面元素
                RedisUtils.setCacheMapValue("multiLanguage:page:" + applicationName + ":" + LocaleContextHolder.getLocale().getLanguage(), k, v);
            }
            RedisUtils.setCacheMapValue(k, LocaleContextHolder.getLocale().getLanguage(), v);
        });
    }

    @Override
    public List<List<String>> listData(String lang, MultiLanguagePageDto req) {
        List<List<String>> res = new ArrayList<>();
        if (req == null) {
            req = new MultiLanguagePageDto();
        }
        Map<String, DictBo> dictMap = dictService.listByDictName(lang, Arrays.asList(SYS_DICT_NAME, TEXT_ATTRIBUTE_DICT_NAME, ELEMENT_TYPE_DICT_NAME, LANGUAGES_DICT_NAME))
                .stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        DictBo languages = dictMap.getOrDefault(LANGUAGES_DICT_NAME, new DictBo());
        List<String> header = new ArrayList<>();
        header.add("多语言ID");
        header.add("多语言code");
        header.add("所属系统");
        header.add("所属页面");
        header.add("功能名称");
        header.add("元素类型");
        header.add("文本属性");
        languages.getNodes().forEach(e -> header.add(e.getDescription()));
        header.add("创建人");
        header.add("创建时间");
        header.add("修改人");
        header.add("修改时间");
        header.add("备注");
        res.add(header);
        List<MultiLanguagePageBo> list = this.getBaseMapper().selectMultiLanguageContentList(req);

        List<Long> ids = list.stream().map(MultiLanguagePageBo::getMultiLanguageId).collect(Collectors.toList());
        List<MultiLanguageContent> contents = new ArrayList<>();
        if (!ids.isEmpty()) {
            contents = multiLanguageContentService.list(new LambdaQueryWrapper<MultiLanguageContent>().in(MultiLanguageContent::getLangId, ids));
        }

        Map<String, List<MultiLanguageContent>> contentGroup = contents.stream().collect(Collectors.groupingBy(MultiLanguageContent::getLangCode));

        MultiLanguagePageDto finalReq = req;
        list.forEach(e -> {
            List<String> record = new ArrayList<>();

            record.add(e.getMultiLanguageId() != null ? e.getMultiLanguageId() + "" : "");
            record.add(e.getLangCode() != null ? e.getLangCode() : "");
            String sysCode = dictMap.getOrDefault(SYS_DICT_NAME, new DictBo()).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getSysCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription();
            record.add(sysCode != null ? sysCode : "");
            record.add(e.getPage() != null ? e.getPage() : "");
            record.add(e.getFunc() != null ? e.getFunc() : "");
            String elementCode = dictMap.getOrDefault(ELEMENT_TYPE_DICT_NAME, new DictBo()).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getElementCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription();
            record.add(elementCode != null ? elementCode : "");
            String textCode = dictMap.getOrDefault(TEXT_ATTRIBUTE_DICT_NAME, new DictBo()).getNodes()
                    .stream()
                    .filter(i -> StringUtils.equals(i.getLabel(), e.getTextCode()))
                    .findFirst()
                    .orElse(new DictNodeBo())
                    .getDescription();
            record.add(textCode != null ? textCode : "");

            List<MultiLanguageContent> contentList = contentGroup.getOrDefault(e.getLangCode(), new ArrayList<>());
            Map<String, MultiLanguageContent> collect = contentList.stream().collect(Collectors.toMap(MultiLanguageContent::getLang, Function.identity()));
            languages.getNodes().forEach(node -> {
                MultiLanguageContent content = collect.getOrDefault(node.getLabel(), new MultiLanguageContent());
                record.add(Optional.ofNullable(content.getContent()).orElse(""));
            });

            record.add(e.getCreateBy() != null ? e.getCreateBy() : "");
            record.add(DateTimeZoneUtil.format(e.getCreateTime(), finalReq.getZone()));
            record.add(e.getUpdateBy() != null ? e.getUpdateBy() : "");
            record.add(DateTimeZoneUtil.format(e.getUpdateTime(), finalReq.getZone()));
            record.add(e.getRemark() != null ? e.getRemark() : "");
            res.add(record);
        });
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importMultiLanguage(MultipartFile file) {
        RLock lock = RedisUtils.getClient().getLock("multiLanguage:import");
        if (lock.isLocked()) {
            return Collections.singletonList("正常操作中，请稍后再试");
        }
        try {
            lock.lock(CommonConstant.TEN, TimeUnit.MINUTES);
            List<String> res = new ArrayList<>();
            List<Map<Integer, String>> data;
            try {
                data = EasyExcel.read(file.getInputStream()).sheet().doReadSync();
            } catch (Exception e) {
                throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_READ_EXCEL_ERROR);
            }
            if (CollectionUtils.isEmpty(data)) {
                throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_EXCEL_EMPTY);
            }
            if (data.size() > 5000) {
                throw ExceptionMessageUtil.getException(ConfigurationErrorCode.MULTI_LANGUAGE_EXCEL_MORE_THAN_5000, data.size());
            }
            data.forEach(e -> e.replaceAll((k, v) -> CsvUtil.unFormat(v)));
            if (!validImport(LocaleContextHolder.getLocale().getLanguage(), data, res)) {
                return res;
            }

            Map<String, DictBo> dictMap = dictService.listByDictName(LocaleContextHolder.getLocale().getLanguage(), Collections.singletonList(LANGUAGES_DICT_NAME))
                    .stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
            DictBo languageCodes = dictMap.get(LANGUAGES_DICT_NAME);
            if (languageCodes == null) {
                languageCodes = new DictBo();
            }
            int s = languageCodes.getNodes().size();

            List<MultiLanguage> byCode = this.list(new LambdaQueryWrapper<MultiLanguage>().in(MultiLanguage::getLangCode, data.stream().map(e -> e.get(1)).collect(Collectors.toSet())));
            Map<String, MultiLanguage> codeDataMap = byCode.stream().collect(Collectors.toMap(MultiLanguage::getLangCode, Function.identity()));
            List<MultiLanguage> languages = new ArrayList<>();
            data.forEach(e -> {
                MultiLanguage l = new MultiLanguage();
                BeanUtils.copyProperties(e, l);
                if (StringUtils.isNotBlank(e.get(0))) {
                    l.setId(Long.parseLong(e.get(0)));
                }
                if (StringUtils.isNotBlank(e.get(1))) {
                    l.setLangCode(e.get(1));
                }
                if (StringUtils.isNotBlank(e.get(2))) {
                    l.setSysCode(e.get(2));
                }
                if (StringUtils.isNotBlank(e.get(3))) {
                    l.setPage(e.get(3));
                }
                if (StringUtils.isNotBlank(e.get(4))) {
                    l.setFunc(e.get(4));
                }
                if (StringUtils.isNotBlank(e.get(5))) {
                    l.setElementCode(e.get(5));
                }
                if (StringUtils.isNotBlank(e.get(6))) {
                    l.setTextCode(e.get(6));
                }
                if (StringUtils.isNotBlank(e.get(7 + s))) {
                    l.setRemark(e.get(7 + s));
                }
                MultiLanguage orDefault = codeDataMap.getOrDefault(e.get(1), new MultiLanguage());
                l.setId(orDefault.getId());
                l.setIsRich(0);
                languages.add(l);
            });
            this.saveOrUpdateBatch(languages);

            // 判断是否有文案，如果没有直接退出
            if (s <= 0) {
                return res;
            }

            Map<String, Map<Integer, String>> readMap = data.stream().collect(Collectors.toMap(e -> e.get(1), Function.identity()));
            Map<String, MultiLanguage> map = languages.stream().collect(Collectors.toMap(MultiLanguage::getLangCode, Function.identity()));
            List<MultiLanguageContent> contents = new ArrayList<>();

            Map<String, Long> contentCodeIdMap = multiLanguageContentService
                    .list(new LambdaQueryWrapper<MultiLanguageContent>().select(MultiLanguageContent::getId, MultiLanguageContent::getLangCode, MultiLanguageContent::getLang).in(MultiLanguageContent::getLangCode, data.stream().map(e -> e.get(1)).collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(e -> e.getLang() + "[SPLIT]" + e.getLangCode(), BaseDo::getId, (v1, v2) -> v2));

            DictBo finalLanguageCodes = languageCodes;
            readMap.forEach((k, v) -> {
                for (int n = 0, m = finalLanguageCodes.getNodes().size(); n < m; n++) {
                    if (v.get(7 + n) != null) {
                        MultiLanguageContent content = new MultiLanguageContent();
                        content.setLangId(map.get(k).getId());
                        content.setLangCode(k);
                        content.setId(contentCodeIdMap.get(finalLanguageCodes.getNodes().get(n).getLabel() + "[SPLIT]" + k));
                        content.setLang(finalLanguageCodes.getNodes().get(n).getLabel());
                        content.setContent(v.get(7 + n));
                        content.setShortContent(v.get(7 + n).length() <= 20 ? v.get(7 + n) : v.get(7 + n).substring(0, 20) + "...");
                        contents.add(content);
                    }
                }

            });
            multiLanguageContentService.saveOrUpdateBatch(contents);

            // 设置redis
            data.forEach(e -> {
                for (int n = 0, m = finalLanguageCodes.getNodes().size(); n < m; n++) {
                    if (e.get(7 + n) != null) {
                        boolean f;
                        try {
                            Long.parseLong(e.get(1));
                            f = true;
                        } catch (Exception ex) {
                            f = false;
                        }
                        if (!f) {
                            // 非数字，页面元素
                            RedisUtils.setCacheMapValue("multiLanguage:page:" + e.get(2) + ":" + finalLanguageCodes.getNodes().get(n).getLabel(), e.get(1), e.get(7 + n));
                        }
                        RedisUtils.setCacheMapValue(e.get(1), finalLanguageCodes.getNodes().get(n).getLabel(), e.get(7 + n));
                    }
                }
            });

            return res;
        } finally {
            lock.unlock();
        }
    }


    @Override
    public List<List<String>> template(String lang) {
        Map<String, DictBo> dictMap = dictService.listByDictName(lang, Collections.singletonList(LANGUAGES_DICT_NAME))
                .stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        DictBo languages = dictMap.get(LANGUAGES_DICT_NAME);
        if (languages == null) {
            languages = new DictBo();
        }
        List<List<String>> res = new ArrayList<>();
        List<String> header = new ArrayList<>();
        header.add("多语言ID");
        header.add("多语言code");
        header.add("所属系统");
        header.add("所属页面");
        header.add("功能名称");
        header.add("元素类型");
        header.add("文本属性");
        languages.getNodes().forEach(e -> header.add(e.getDescription()));
        header.add("备注");
        res.add(header);
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MultiLanguageBo simpleCreateRichMultiLanguage(String applicationName, String content) {
        String sysCode = ApplicationEnum.fromName(applicationName).getName();
        MultiLanguage language = new MultiLanguage();
        language.setLangCode(SnowFlake.nextId() + "");
        language.setSysCode(sysCode);
        language.setIsRich(1);
        this.save(language);
        MultiLanguageContent item = new MultiLanguageContent();
        item.setLangId(language.getId());
        item.setLangCode(language.getLangCode());
        item.setLang(LocaleContextHolder.getLocale().getLanguage());
        item.setContent(content);
        item.setShortContent(content.length() <= 20 ? content : content.substring(0, 20) + "...");
        multiLanguageContentService.save(item);
        // 设置redis
        boolean f;
        try {
            Long.parseLong(item.getLangCode());
            f = true;
        } catch (Exception ex) {
            f = false;
        }
        if (!f) {
            // 非数字，页面元素
            RedisUtils.setCacheMapValue("multiLanguage:page:" + sysCode + ":" + item.getLang(), item.getLangCode(), item.getContent());
        }
        RedisUtils.setCacheMapValue(item.getLangCode(), item.getLang(), item.getContent());
        MultiLanguageBo bo = new MultiLanguageBo();
        BeanUtils.copyProperties(language, bo);
        bo.setLangId(language.getId());
        return bo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLangId(Long langId) {
        if (langId != null) {
            this.removeById(langId);
            multiLanguageContentService.remove(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangId, langId));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLangCode(String langCode) {
        if (langCode != null) {
            this.remove(new LambdaQueryWrapper<MultiLanguage>().eq(MultiLanguage::getLangCode, langCode));
            multiLanguageContentService.remove(new LambdaQueryWrapper<MultiLanguageContent>().eq(MultiLanguageContent::getLangCode, langCode));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLangIds(List<Long> langIds) {
        if (!CollectionUtils.isEmpty(langIds)) {
            deleteRedisByLangIds(langIds);
            this.removeBatchByIds(langIds);
            multiLanguageContentService.remove(new LambdaQueryWrapper<MultiLanguageContent>().in(MultiLanguageContent::getLangId, langIds));
        }
    }

    private void deleteRedisByLangIds(List<Long> langIds) {
        List<MultiLanguage> multiLanguages = this.listByIds(langIds);
        List<String> langCodes = multiLanguages.stream().map(MultiLanguage::getLangCode).collect(Collectors.toList());
        RedisUtils.deleteObject(langCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLangCodes(List<String> langCodes) {
        if (!CollectionUtils.isEmpty(langCodes)) {
            this.remove(new LambdaQueryWrapper<MultiLanguage>().in(MultiLanguage::getLangCode, langCodes));
            RedisUtils.deleteObject(langCodes);
            multiLanguageContentService.remove(new LambdaQueryWrapper<MultiLanguageContent>().in(MultiLanguageContent::getLangCode, langCodes));
        }
    }

    private Map<String, String> labels(String lang, String dictName) {
        List<DictBo> dict = dictService.listByDictName(lang, Collections.singletonList(dictName));
        List<DictNodeBo> dictNodes = dict.get(0).getNodes();
        Map<String, String> map = new HashMap<>(dictNodes.size());
        dictNodes.forEach(e -> {
            map.put(e.getDescription(), e.getLabel());
        });
        return map;
    }

    private boolean validImport(String lang, List<Map<Integer, String>> data, List<String> res) {
        Map<String, String> system = labels(lang, SYS_DICT_NAME);
        Map<String, String> elementType = labels(lang, ELEMENT_TYPE_DICT_NAME);
        Map<String, String> textAttribute = labels(lang,TEXT_ATTRIBUTE_DICT_NAME);
        Map<String, DictBo> dictMap = dictService.listByDictName(lang, Collections.singletonList(LANGUAGES_DICT_NAME))
                .stream().collect(Collectors.toMap(DictBo::getDictName, Function.identity()));
        DictBo languages = dictMap.get(LANGUAGES_DICT_NAME);
        if (languages == null) {
            languages = new DictBo();
        }
        int s = languages.getNodes().size();

        List<Integer> idString = new ArrayList<>();
        Map<String, List<Integer>> idMap = new HashMap<>();
        Map<String, List<Integer>> codeMap = new HashMap<>();
        List<Integer> codeNullFlag = new ArrayList<>();
        List<Integer> systemNullFlag = new ArrayList<>();
        List<Integer> systemFlag = new ArrayList<>();
        List<Integer> elementTypeFlag = new ArrayList<>();
        List<Integer> textAttributeFlag = new ArrayList<>();
        List<Integer> zhContentNullFlag = new ArrayList<>();
        List<Integer> codeSizeFlag = new ArrayList<>();
        List<Integer> pageSizeFlag = new ArrayList<>();
        List<Integer> funcSizeFlag = new ArrayList<>();
        List<Integer> remarkSizeFlag = new ArrayList<>();

        for (int i = 2, j = data.size() + 2; i < j; i++) {
            Map<Integer, String> read = data.get(i - 2);

            if (StringUtils.isNotBlank(read.get(0))) {
                try {
                    Long.parseLong(read.get(0));
                } catch (Exception e) {
                    idString.add(i);
                }
                if (idMap.get(read.get(0)) == null) {
                    List<Integer> ids = new ArrayList<>();
                    ids.add(i);
                    idMap.put(read.get(0), ids);
                } else {
                    idMap.get(read.get(0)).add(i);
                }
            }

            if (StringUtils.isNotBlank(read.get(1))) {
                if (codeMap.get(read.get(1)) == null) {
                    List<Integer> codes = new ArrayList<>();
                    codes.add(i);
                    codeMap.put(read.get(1), codes);
                } else {
                    codeMap.get(read.get(1)).add(i);
                }
                if (read.get(1).length() > TEXT_SIZE) {
                    codeSizeFlag.add(i);
                }
            } else {
                codeNullFlag.add(i);
            }

            if (StringUtils.isBlank(read.get(2))) {
                systemNullFlag.add(i);
            } else if (!system.containsKey(read.get(2))) {
                systemFlag.add(i);
            } else {
                read.put(2, system.get(read.get(2)));
            }

            if (StringUtils.isNotBlank(read.get(3)) && read.get(3).length() > TEXT_SIZE) {
                pageSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.get(4)) && read.get(4).length() > TEXT_SIZE) {
                funcSizeFlag.add(i);
            }

            if (StringUtils.isNotBlank(read.get(5))) {
                if (!elementType.containsKey(read.get(5))) {
                    elementTypeFlag.add(i);
                } else {
                    read.put(5, elementType.get(read.get(5)));
                }
            }
            if (StringUtils.isNotBlank(read.get(6))) {
                if (!textAttribute.containsKey(read.get(6))) {
                    textAttributeFlag.add(i);
                } else {
                    read.put(6, textAttribute.get(read.get(6)));
                }
            }

            for (int n = 0, m = languages.getNodes().size(); n < m; n++) {
                if ("zh".equals(languages.getNodes().get(n).getLabel())) {
                    if (StringUtils.isBlank(read.get(7 + n))) {
                        zhContentNullFlag.add(i);
                    }
                    break;
                }
            }

            if (StringUtils.isNotBlank(read.get(7 + s)) && read.get(7 + s).length() > TEXT_AREA_SIZE) {
                remarkSizeFlag.add(i);
            }
        }
        // 检测id是否为数字
        if (idString.size() > 0) {
            res.add("第" + idString.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【多语言ID】非法");
        }
        // 检测id是否唯一
        for (Map.Entry<String, List<Integer>> entry : idMap.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.size() != 1) {
                res.add("第" + value.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【多语言ID】重复");
            }
        }
        // 检测code是否必填
        if (codeNullFlag.size() > 0) {
            res.add("第" + codeNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【多语言code】为空");
        }
        // 检测code长度
        if (codeSizeFlag.size() > 0) {
            res.add("第" + codeSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【多语言code】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测code是否唯一
        for (Map.Entry<String, List<Integer>> entry : codeMap.entrySet()) {
            List<Integer> v = entry.getValue();
            if (v.size() != 1) {
                res.add("第" + v.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【多语言code】表内重复");
            }
        }
        // 检测system是否必填
        if (systemNullFlag.size() > 0) {
            res.add("第" + systemNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【所属系统】为空");
        }
        // 检测所属系统是否正确
        if (systemFlag.size() > 0) {
            res.add("第" + systemFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【所属系统】不正确");
        }
        // 检测page长度
        if (pageSizeFlag.size() > 0) {
            res.add("第" + pageSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【所属页面】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测func长度
        if (funcSizeFlag.size() > 0) {
            res.add("第" + funcSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【功能名称】内容超过" + TEXT_SIZE + "个字符");
        }
        // 检测元素类型是否正确
        if (elementTypeFlag.size() > 0) {
            res.add("第" + elementTypeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【元素类型】不正确");
        }
        // 检测文本属性是否正确
        if (textAttributeFlag.size() > 0) {
            res.add("第" + textAttributeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【文本属性】不正确");
        }
        // 检测中文是否必填
        if (zhContentNullFlag.size() > 0) {
            res.add("第" + zhContentNullFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【ZH】为空");
        }
        // 检测remark长度
        if (remarkSizeFlag.size() > 0) {
            res.add("第" + remarkSizeFlag.stream().map(String::valueOf).collect(Collectors.joining("，")) + "行，【备注】内容超过" + TEXT_AREA_SIZE + "个字符");
        }

        if (!res.isEmpty()) {
            return false;
        }

        List<Long> ids = data.stream().filter(e -> StringUtils.isNotBlank(e.get(0))).map(e -> Long.parseLong(e.get(0))).distinct().collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<MultiLanguage> multiLanguages = this.listByIds(ids);
            Map<Long, String> idCodeMap = multiLanguages.stream().collect(Collectors.toMap(BaseDo::getId, MultiLanguage::getLangCode));
            Set<Long> existIds = idCodeMap.keySet();
            for (int i = 2, j = data.size() + 2; i < j; i++) {
                Map<Integer, String> read = data.get(i - 2);
                if (StringUtils.isNotBlank(read.get(0)) && !existIds.contains(Long.parseLong(read.get(0)))) {
                    res.add("第" + i + "行，【多语言ID】不存在");
                } else if (StringUtils.isNotBlank(read.get(0)) && !StringUtils.equals(read.get(1), idCodeMap.get(Long.parseLong(read.get(0))))) {
                    res.add("第" + i + "行，【多语言ID】和【多语言code】不匹配");
                }
            }
        }

        return res.isEmpty();
    }

}
