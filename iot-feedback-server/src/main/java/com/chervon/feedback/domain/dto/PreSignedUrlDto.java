package com.chervon.feedback.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2022-06-18
 */
@Data
public class PreSignedUrlDto {

    /**
     * 文件名称
     */
    @NotEmpty(message = "fileName不能为空")
    @ApiModelProperty("文件名称")
    String fileName;

    @NotEmpty(message = "fileType不能为空")
    @ApiModelProperty("文件类型：1 picture，2 parts，3 product")
    String fileType;

    @ApiModelProperty("配件Id或者产品Id")
    Long id;
}
