package com.chervon.commons.db.mongo.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * mongo分页实体
 *
 * <AUTHOR>
 * @date 2022/02/09
 */
@Data
public class PageModel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 当前页
     */
    private Integer pageNum = 1;
    /**
     * 当前页条数
     */
    private Integer pageSize = 10;
    /**
     * 总共的条数
     */
    private Long total;
    /**
     * 总共的页数
     */
    private Integer pages;
    /**
     * 实体类集合
     */
    private List<?> list;

    public PageModel(Integer pageNum, Integer pageSize, Long total, Integer pages, List<?> list) {
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.total = total;
        this.pages = pages;
        this.list = list;
    }

    public PageModel() {
    }

}
