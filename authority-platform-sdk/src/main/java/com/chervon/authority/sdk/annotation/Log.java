package com.chervon.authority.sdk.annotation;

import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.authority.sdk.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 自定义审计日志记录注解
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 模块
     */
    String title() default "";

    /**
     * 功能
     */
    BusinessType businessType() default BusinessType.VIEW;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;
}
