package com.chervon.common.web.core;

import com.chervon.common.core.utils.SpringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.HashMap;
import java.util.Map;

/**
 * translate转换器实例工厂
 * <AUTHOR> 2022/10/28
 */
@Component
public class TranslateServiceFactory {

    private static Map<String,TranslateService> handleCollection=new HashMap<>();

    public static TranslateService getInstance(String code){
        if(CollectionUtils.isEmpty(handleCollection)){
            initInstance();
        }
        return handleCollection.get(code);
    }

    private static synchronized void initInstance() {
        final Map<String, TranslateService> serviceMap = SpringUtils.getBeansOfType(TranslateService.class);
        for (TranslateService bean : serviceMap.values()) {
            handleCollection.put(bean.getInstanceCode(), bean);
        }
    }

}
