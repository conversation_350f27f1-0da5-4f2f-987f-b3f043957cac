package com.chervon.technology.api.vo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
* OTA升级任务检查结果vo
 */
@ApiModel(description = "升级任务文档")
@Data
public class OtaCheckVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否有升级任务
     **/
    @ApiModelProperty(value = "是否有升级任务", required = true)
    private Boolean hasOtaJob=false;
    /**
     * 自定义版本号，运营版本号
     **/
    @ApiModelProperty("自定义版本号，运营版本号")
    private String customVersion;

    @ApiModelProperty("错误信息")
    private String errorMsg;
}
