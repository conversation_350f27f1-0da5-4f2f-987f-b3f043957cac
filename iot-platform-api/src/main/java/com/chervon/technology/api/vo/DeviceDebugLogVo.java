package com.chervon.technology.api.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022年12月29日
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceDebugLogVo implements Serializable {

	@ApiModelProperty("设备id")
	@NotEmpty
	private String deviceId;

	@ApiModelProperty("文件名称")
	@NotEmpty
	private String fileName;

	@ApiModelProperty("文件大小")
	@NotNull
	private Integer fileSize;

	@ApiModelProperty("总的分段数")
	@NotNull
	private Integer totalPartNum;

}
