package com.chervon.technology.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-02-15 15:14
 **/
@Data
public class DeviceBindBo implements Serializable {
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备sn
     */
    private String sn;
    /**
     * 产品ID
     * <p>
     * product表id字段
     */
    private Long productId;
    /**
     * 产品类型
     * <p>
     * product表的productType字段
     * directConnectedDevice：直连设备
     * gatewayDevice：网关设备
     * gatewaySubDevice：网关子设备
     * notIotDevice：非Iot设备
     * oldIotDevice：老iot设备
     */
    private String productType;
    /**
     * 商品型号Model#
     * <p>
     * 设置设备默认昵称时需要
     */
    private String commodityModel;

    /**
     * 绑定结果 0 success 1 已绑定  3设备已停用
     */
    private int result;

    /**
     * 设备注册返回的设备昵称
     */
    private String deviceNickName;
    /**
     * 联网类型
     */
    private String networkModes;
}
