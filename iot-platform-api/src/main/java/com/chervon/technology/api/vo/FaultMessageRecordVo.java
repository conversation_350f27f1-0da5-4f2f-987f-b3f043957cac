package com.chervon.technology.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年12月20日
 */
@Data
public class FaultMessageRecordVo implements Serializable {
	@ApiModelProperty(value = "系统消息id")
	private Long msgId;

	@ApiModelProperty(value = "消息类型，0系统消息，1营销消息, 2设备消息")
	private Integer messageType;

	@ApiModelProperty(value = "消息标题")
	private String title;

	@ApiModelProperty(value = "消息标题多语言id")
	private Long titleLangId;

	@ApiModelProperty(value = "消息内容")
	private String content;

	@ApiModelProperty(value = "消息内容多语言id")
	private Long contentLangId;

	@ApiModelProperty(value = "推送类型code")
	private List<String> pushTypeCodes;

	/**
	 * 产品id
	 */
	@ApiModelProperty("产品id")
	private Long productId;


	@ApiModelProperty(value = "推送条数")
	private Integer pushAllNum;

	@ApiModelProperty(value = "推送成功条数")
	private Integer pushSuccessNum;

	@ApiModelProperty(value = "推送失败条数")
	private Integer pushFailNum;
}
