package com.chervon.technology.api.toruleengine;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * <AUTHOR>
 * @className CheckJobDto
 * @description 测试升级
 * @date 2022年10月21日
 */
@ApiModel("测试升级")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckJobDto {

	/**
	 * 消息唯一标识
	 */
	@ApiModelProperty("消息唯一标识")
	private String messageId;

	@ApiModelProperty("客户端标识 android：安卓，iOS: ios端，rn：rn端，wifi: wifi设备端，4g: 4g端")
	private String clientType;

	/**
	 * 设备id
	 **/
	@ApiModelProperty("设备id")
	private String deviceId;

	/**
	 * 是否为单MCU升级，true 单MCU升级，false 整合升级
	 **/
	@ApiModelProperty("是否为单MCU升级，true 单MCU升级，false 整合升级")
	private Boolean singleMcu;

	/**
	 * 语言
	 **/
	@ApiModelProperty("语言")
	private String lang;

	/**
	 * 短token 可以获取userID
	 **/
	@ApiModelProperty("短token 可以获取userID")
	private String shortToken;

	/**
	 * 总成零件map，key为总成零件号，value为总成零件版本号
	 **/
	@ApiModelProperty("总成零件map，key为总成零件号，value为总成零件版本号")
	private Map<String, String> componentMap;


}
