package com.chervon.technology.api;

import com.chervon.technology.api.vo.DeviceCodeRpcVo;

import java.util.List;

/**
 * 设备信息相关接口
 * <AUTHOR>
 * @since 2022-08-16 18:12
 **/
public interface RemoteDeviceCodeService {
    /**
     * 根据设备deviceId获取设备多码
     * 只会返回deviceId,sn,status,productSnCode四个值
     * @param deviceId 设备ID
     * @return DeviceCodeRpcVo
     */
    DeviceCodeRpcVo deviceCodeDetail(String deviceId);

    /**
     * 根据设备sn获取设备多码
     * 只会返回deviceId,sn,status,productSnCode四个值
     * @param sn 设备SN
     * @return DeviceCodeRpcVo
     */
    DeviceCodeRpcVo deviceCodeDetailBySn(String sn);

    /**
     * 根据设备deviceId列表获取设备多码列表
     * 只会返回deviceId,status
     * @param deviceIdList 设备Id列表
     * @return 设备多码Vo列表
     */
    List<DeviceCodeRpcVo> listByDeviceId(List<String> deviceIdList);

    /**
     * 非IOT设备绑定时根据SN生成一个DeviceId并创建为一条多码数据
     * @param sn Serial Number
     * @param productSnCode 产品SnCode
     * @return 设备ID deviceId
     */
    String createDeviceCodeBySn(String sn, String productSnCode);
}
