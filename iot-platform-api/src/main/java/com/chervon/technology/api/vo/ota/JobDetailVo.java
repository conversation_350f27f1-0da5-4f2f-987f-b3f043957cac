package com.chervon.technology.api.vo.ota;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className JobListVo
 * @description
 * @date 2022/7/13 11:52
 */
@Data
@ApiModel("升级任务列表")
public class JobDetailVo implements Serializable {

    private static final long serialVersionUID = -6756066875049912852L;

    /**
     * 任务ID
     **/
    @ApiModelProperty("任务ID")
    private Long jobId;

    /**
     * 产品PID
     **/
    @ApiModelProperty("产品PID")
    private Long productId;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String categoryName;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String brandName;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 商品型号
     */
    @ApiModelProperty("商品型号")
    private String commodityModel;

    /**
     * 技术版本号
     **/
    @ApiModelProperty("技术版本号")
    private String technologyVersion;

    /**
     * 用户版本号
     **/
    @ApiModelProperty("用户版本号")
    private String customVersion;

    /**
     * 固件数量
     **/
    @ApiModelProperty("固件数量")
    private Integer packageCount;

    /**
     * 固件列表
     **/
    @ApiModelProperty("固件列表")
    private List<FirmwareVo> firmwares;

}
