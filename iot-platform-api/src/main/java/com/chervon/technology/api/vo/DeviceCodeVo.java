package com.chervon.technology.api.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.config.LocalDateTimeSerializerConfig;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class DeviceCodeVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 多码表中的id
     */
    @ApiModelProperty("多码表中的id")
    @Alias("ID")
    private Long id;

    /**
     * 设备中烧录的Id
     */
    @ApiModelProperty("设备中烧录的Id")
    @Alias("设备ID")
    private String deviceId;

    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    @Alias("设备SN")
    private String sn;

    /**
     * 设备moCode
     */
    @ApiModelProperty("设备moCode")
    @Alias("MO code")
    private String moCode;

    /**
     * MES #
     */
    @ApiModelProperty("MES #")
    @Alias("MES #")
    private String mes;

    /**
     * 设备itemCode
     */
    @ApiModelProperty("设备itemCode")
    @Alias("ITEM code")
    private String itemCode;

    /**
     * sim卡 iccid
     */
    @ApiModelProperty("sim卡 iccid")
    @Alias("ICCID")
    private String iccid;

    /**
     * 设备生成日期
     */
    @ApiModelProperty("设备生成日期")
    @Alias("Production Date")
    private Timestamp productionDate;

    /**
     * 状态：0废弃 1正常
     */
    @ApiModelProperty("状态：0废弃 1正常")
    @Alias("状态")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @Alias("创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @Alias("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    @Alias("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @Alias("更新时间")
    @JsonSerialize(using = LocalDateTimeSerializerConfig.LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;
}
