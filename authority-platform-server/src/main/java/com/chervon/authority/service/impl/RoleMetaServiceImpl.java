package com.chervon.authority.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.authority.domain.entity.RoleMeta;
import com.chervon.authority.mapper.RoleMetaMapper;
import com.chervon.authority.service.RoleMetaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色元数据关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
@Service
public class RoleMetaServiceImpl extends ServiceImpl<RoleMetaMapper, RoleMeta> implements RoleMetaService {

    @Override
    public RoleMeta findByRoleId(Long roleId) {
        LambdaQueryWrapper<RoleMeta> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RoleMeta::getRoleId, roleId);
        return this.getOne(wrapper);
    }
}
