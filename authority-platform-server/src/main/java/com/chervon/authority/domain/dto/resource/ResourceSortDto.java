package com.chervon.authority.domain.dto.resource;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-06-21
 * 资源排序
 */
@Data
public class ResourceSortDto {

    /**
     * 系统Id
     */
    @NotEmpty
    private String appId;

    /**
     * 父级Id,顶级菜单为0
     */
    @NotNull
    private Long parentId;

    /**
     * 重新排序后的Id列表，按照升序
     */
    @NotNull
    private List<Long> sortIds;
}
