package com.chervon.usercenter.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.UUIDUtils;
import com.chervon.usercenter.api.dto.AppUserPageDto;
import com.chervon.usercenter.api.dto.sf.SfUserAddDto;
import com.chervon.usercenter.api.service.SaleForceService;
import com.chervon.usercenter.api.vo.AppUserVo;
import com.chervon.usercenter.api.vo.UserVo;
import com.chervon.usercenter.domain.model.user.UserRepository;
import com.chervon.usercenter.infrastructure.entity.UserDo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RemoteAppUserServiceImpl单元测试类
 * 
 * 测试覆盖范围：
 * 1. 分页查询用户（通过邮箱和用户ID列表）
 * 2. 列表查询用户（通过邮箱和用户ID列表）
 * 3. 分页查询用户（通过复杂条件）
 * 4. 列表查询用户（通过复杂条件）
 * 5. 根据用户ID获取用户详情
 * 6. 根据邮箱获取用户详情
 * 7. 根据用户ID列表获取用户Map
 * 8. 获取所有用户ID
 * 9. 获取SF用户ID
 * 10. 根据用户ID获取应用类型代码
 * 11. 根据用户ID列表获取用户邮箱信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("远程应用用户服务实现类单元测试")
class RemoteAppUserServiceImplTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private SaleForceService saleForceService;

    @InjectMocks
    private RemoteAppUserServiceImpl remoteAppUserService;

    // 测试常量
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final Long TEST_USER_ID = 1001L;
    private static final String TEST_SF_USER_ID = "SF123456";
    private static final String TEST_APP_TYPE_CODE = "android";
    private static final String TEST_FIRST_NAME = "John";
    private static final String TEST_LAST_NAME = "Doe";
    private static final String TEST_PHONE = "1234567890";
    private static final String TEST_COUNTRY = "US";
    private static final String TEST_PASSWORD = "password123";
    private static final String TEST_AES_PASSWORD = "aesPassword";
    private static final String TEST_AES_SALT = "aesSalt";
    private static final String TEST_ADDRESS_LINE = "123 Main St";
    private static final String TEST_POST_CODE = "12345";

    private UserDo mockUserDo;
    private AppUserPageDto mockAppUserPageDto;

    /**
     * MyBatis-Plus Lambda缓存初始化方法
     * 根据.clinerules规范实现
     */
    public static void initEntityTableInfo(Class<?>... entityClasses) {
        for (Class<?> entityClass : entityClasses) {
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), entityClass);
        }
    }

    @BeforeEach
    void setUp() {
        // Given - 初始化 MyBatis-Plus Lambda 缓存
        initEntityTableInfo(UserDo.class);
        
        // Given - 初始化测试数据
        mockUserDo = createMockUserDo();
        mockAppUserPageDto = createMockAppUserPageDto();
    }

    @Nested
    @DisplayName("分页查询用户测试")
    class PageQueryTests {

        @Test
        @DisplayName("通过邮箱和用户ID列表分页查询用户 - 正常情况")
        void testPageAppUserByEmailAndUserIds_Success() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID, 1002L);
            Page<UserDo> mockPage = createMockPage();
            when(userRepository.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<AppUserVo> result = remoteAppUserService.pageAppUserByEmailAndUserIds(TEST_EMAIL, userIds, 1, 10);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getList().size());
            assertEquals(TEST_USER_ID.toString(), result.getList().get(0).getUserId());
            assertEquals(TEST_EMAIL, result.getList().get(0).getEmail());
            verify(userRepository, times(1)).page(any(Page.class), any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("通过邮箱和用户ID列表分页查询用户 - 空邮箱")
        void testPageAppUserByEmailAndUserIds_EmptyEmail() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID);
            Page<UserDo> mockPage = createMockPage();
            when(userRepository.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<AppUserVo> result = remoteAppUserService.pageAppUserByEmailAndUserIds("", userIds, 1, 10);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            verify(userRepository, times(1)).page(any(Page.class), any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("通过邮箱和用户ID列表分页查询用户 - 空用户ID列表")
        void testPageAppUserByEmailAndUserIds_EmptyUserIds() {
            // Given
            Page<UserDo> mockPage = createMockPage();
            when(userRepository.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            // When
            PageResult<AppUserVo> result = remoteAppUserService.pageAppUserByEmailAndUserIds(TEST_EMAIL, Collections.emptyList(), 1, 10);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            verify(userRepository, times(1)).page(any(Page.class), any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("列表查询用户测试")
    class ListQueryTests {

        @Test
        @DisplayName("通过邮箱和用户ID列表查询用户列表 - 正常情况")
        void testListAppUserByEmailAndUserIds_Success() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID);
            List<UserDo> mockUserList = Arrays.asList(mockUserDo);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(mockUserList);

            // When
            List<AppUserVo> result = remoteAppUserService.listAppUserByEmailAndUserIds(TEST_EMAIL, userIds);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_USER_ID.toString(), result.get(0).getUserId());
            assertEquals(TEST_EMAIL, result.get(0).getEmail());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("通过邮箱和用户ID列表查询用户列表 - 空结果")
        void testListAppUserByEmailAndUserIds_EmptyResult() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

            // When
            List<AppUserVo> result = remoteAppUserService.listAppUserByEmailAndUserIds(TEST_EMAIL, userIds);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("复杂条件查询测试")
    class ComplexQueryTests {

        @Test
        @DisplayName("分页查询用户 - 正常情况")
        void testPageAppUser_Success() {
            // Given
            Page<UserDo> mockPage = createMockPage();
            when(userRepository.page(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(mockPage);

            try (MockedStatic<UUIDUtils> mockedUUIDUtils = mockStatic(UUIDUtils.class)) {
                mockedUUIDUtils.when(UUIDUtils::randomShortUUID).thenReturn("shortUUID123");

                // When
                PageResult<AppUserVo> result = remoteAppUserService.pageAppUser(mockAppUserPageDto);

                // Then
                assertNotNull(result);
                assertEquals(1, result.getPageNum());
                assertEquals(10, result.getPageSize());
                assertEquals(1, result.getTotal());
                assertEquals(1, result.getList().size());
                assertEquals("shortUUID123", result.getList().get(0).getId());
                assertEquals(TEST_USER_ID.toString(), result.getList().get(0).getUserId());
                verify(userRepository, times(1)).page(any(Page.class), any(LambdaQueryWrapper.class));
            }
        }

        @Test
        @DisplayName("列表查询用户 - 正常情况")
        void testListAppUser_Success() {
            // Given
            List<UserDo> mockUserList = Arrays.asList(mockUserDo);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(mockUserList);

            // When
            List<AppUserVo> result = remoteAppUserService.listAppUser(mockAppUserPageDto);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_USER_ID.toString(), result.get(0).getUserId());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("单个用户查询测试")
    class SingleUserQueryTests {

        @Test
        @DisplayName("根据用户ID获取用户详情 - 正常情况")
        void testGetAppUser_Success() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(mockUserDo);

            try (MockedStatic<ConvertUtil> mockedConvertUtil = mockStatic(ConvertUtil.class)) {
                AppUserVo mockAppUserVo = new AppUserVo();
                mockAppUserVo.setEmail(TEST_EMAIL);
                mockedConvertUtil.when(() -> ConvertUtil.convert(mockUserDo, AppUserVo.class)).thenReturn(mockAppUserVo);

                // When
                AppUserVo result = remoteAppUserService.getAppUser(TEST_USER_ID);

                // Then
                assertNotNull(result);
                assertEquals(TEST_USER_ID.toString(), result.getUserId());
                assertEquals(TEST_EMAIL, result.getEmail());
                verify(userRepository, times(1)).getById(TEST_USER_ID);
            }
        }

        @Test
        @DisplayName("根据用户ID获取用户详情 - 用户不存在")
        void testGetAppUser_UserNotFound() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(null);

            // When
            AppUserVo result = remoteAppUserService.getAppUser(TEST_USER_ID);

            // Then
            assertNull(result);
            verify(userRepository, times(1)).getById(TEST_USER_ID);
        }

        @Test
        @DisplayName("根据邮箱获取用户详情 - 正常情况")
        void testGetAppUserByEmail_Success() {
            // Given
            when(userRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(mockUserDo);

            try (MockedStatic<ConvertUtil> mockedConvertUtil = mockStatic(ConvertUtil.class)) {
                AppUserVo mockAppUserVo = new AppUserVo();
                mockAppUserVo.setEmail(TEST_EMAIL);
                mockedConvertUtil.when(() -> ConvertUtil.convert(mockUserDo, AppUserVo.class)).thenReturn(mockAppUserVo);

                // When
                AppUserVo result = remoteAppUserService.getAppUserByEmail(TEST_EMAIL);

                // Then
                assertNotNull(result);
                assertEquals(TEST_USER_ID.toString(), result.getUserId());
                assertEquals(TEST_EMAIL, result.getEmail());
                verify(userRepository, times(1)).getOne(any(LambdaQueryWrapper.class));
            }
        }

        @Test
        @DisplayName("根据邮箱获取用户详情 - 用户不存在")
        void testGetAppUserByEmail_UserNotFound() {
            // Given
            when(userRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            // When
            AppUserVo result = remoteAppUserService.getAppUserByEmail(TEST_EMAIL);

            // Then
            assertNull(result);
            verify(userRepository, times(1)).getOne(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("用户Map查询测试")
    class UserMapQueryTests {

        @Test
        @DisplayName("根据用户ID列表获取用户Map - 正常情况")
        void testListAppUserMap_Success() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID, 1002L);
            List<UserDo> mockUserList = Arrays.asList(mockUserDo);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(mockUserList);

            try (MockedStatic<ConvertUtil> mockedConvertUtil = mockStatic(ConvertUtil.class)) {
                AppUserVo mockAppUserVo = new AppUserVo();
                mockAppUserVo.setUserId(TEST_USER_ID.toString());
                mockAppUserVo.setEmail(TEST_EMAIL);
                mockAppUserVo.setName(TEST_FIRST_NAME + " " + TEST_LAST_NAME);
                mockedConvertUtil.when(() -> ConvertUtil.convert(mockUserDo, AppUserVo.class)).thenReturn(mockAppUserVo);

                // When
                Map<String, AppUserVo> result = remoteAppUserService.listAppUserMap(userIds);

                // Then
                assertNotNull(result);
                assertEquals(1, result.size());
                assertTrue(result.containsKey(TEST_USER_ID.toString()));
                assertEquals(TEST_EMAIL, result.get(TEST_USER_ID.toString()).getEmail());
                assertEquals(TEST_FIRST_NAME + " " + TEST_LAST_NAME, result.get(TEST_USER_ID.toString()).getName());
                verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
            }
        }

        @Test
        @DisplayName("根据用户ID列表获取用户Map - 空用户ID列表")
        void testListAppUserMap_EmptyUserIds() {
            // Given
            List<Long> userIds = Collections.emptyList();

            // When
            Map<String, AppUserVo> result = remoteAppUserService.listAppUserMap(userIds);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(userRepository, never()).list(any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("根据用户ID列表获取用户Map - null用户ID列表")
        void testListAppUserMap_NullUserIds() {
            // Given & When
            Map<String, AppUserVo> result = remoteAppUserService.listAppUserMap(null);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(userRepository, never()).list(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("所有用户ID查询测试")
    class AllUserIdsQueryTests {

        @Test
        @DisplayName("获取所有用户ID - 正常情况")
        void testListAllUserIds_Success() {
            // Given
            List<UserDo> mockUserList = Arrays.asList(mockUserDo);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(mockUserList);

            // When
            List<Long> result = remoteAppUserService.listAllUserIds();

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_USER_ID, result.get(0));
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("获取所有用户ID - 空结果")
        void testListAllUserIds_EmptyResult() {
            // Given
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

            // When
            List<Long> result = remoteAppUserService.listAllUserIds();

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("应用类型代码查询测试")
    class AppTypeCodeQueryTests {

        @Test
        @DisplayName("根据用户ID获取应用类型代码 - 正常情况")
        void testGetUserAppTypeCodeByUserId_Success() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(mockUserDo);

            // When
            String result = remoteAppUserService.getUserAppTypeCodeByUserId(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertEquals(TEST_APP_TYPE_CODE, result);
            verify(userRepository, times(1)).getById(TEST_USER_ID);
        }

        @Test
        @DisplayName("根据用户ID获取应用类型代码 - 用户不存在")
        void testGetUserAppTypeCodeByUserId_UserNotFound() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(null);

            // When
            String result = remoteAppUserService.getUserAppTypeCodeByUserId(TEST_USER_ID);

            // Then
            assertNull(result);
            verify(userRepository, times(1)).getById(TEST_USER_ID);
        }
    }

    @Nested
    @DisplayName("用户邮箱查询测试")
    class UserEmailQueryTests {

        @Test
        @DisplayName("根据用户ID列表获取用户邮箱信息 - 正常情况")
        void testListUserEmailByUserIds_Success() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID);
            List<UserDo> mockUserList = Arrays.asList(mockUserDo);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(mockUserList);

            // When
            List<UserVo> result = remoteAppUserService.listUserEmailByUserIds(userIds);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(TEST_USER_ID, result.get(0).getId());
            assertEquals(TEST_EMAIL, result.get(0).getEmail());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }

        @Test
        @DisplayName("根据用户ID列表获取用户邮箱信息 - 空结果")
        void testListUserEmailByUserIds_EmptyResult() {
            // Given
            List<Long> userIds = Arrays.asList(TEST_USER_ID);
            when(userRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

            // When
            List<UserVo> result = remoteAppUserService.listUserEmailByUserIds(userIds);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(userRepository, times(1)).list(any(LambdaQueryWrapper.class));
        }
    }

    @Nested
    @DisplayName("SF用户ID查询测试")
    class SfUserIdQueryTests {

        @Test
        @DisplayName("获取SF用户ID - 用户已有SF用户ID")
        void testGetSfUserId_UserAlreadyHasSfUserId() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(mockUserDo);

            // When
            String result = remoteAppUserService.getSfUserId(TEST_USER_ID);

            // Then
            assertNotNull(result);
            assertEquals(TEST_SF_USER_ID, result);
            verify(userRepository, times(1)).getById(TEST_USER_ID);
            verify(saleForceService, never()).addSfUser(any(SfUserAddDto.class));
            verify(userRepository, never()).update(any(UserDo.class), any(LambdaUpdateWrapper.class));
        }

        @Test
        @DisplayName("获取SF用户ID - 用户不存在")
        void testGetSfUserId_UserNotFound() {
            // Given
            when(userRepository.getById(TEST_USER_ID)).thenReturn(null);

            // When
            String result = remoteAppUserService.getSfUserId(TEST_USER_ID);

            // Then
            assertNull(result);
            verify(userRepository, times(1)).getById(TEST_USER_ID);
            verify(saleForceService, never()).addSfUser(any(SfUserAddDto.class));
        }

        @Test
        @DisplayName("获取SF用户ID - 需要创建新的SF用户")
        void testGetSfUserId_CreateNewSfUser() {
            // Given
            UserDo userWithoutSfId = createMockUserDo();
            userWithoutSfId.setSfUserId(null); // 没有SF用户ID
            when(userRepository.getById(TEST_USER_ID)).thenReturn(userWithoutSfId);
            when(saleForceService.addSfUser(any(SfUserAddDto.class))).thenReturn(TEST_SF_USER_ID);
            when(userRepository.update(any(UserDo.class), any(LambdaUpdateWrapper.class))).thenReturn(true);

            try (MockedStatic<AesUtils> mockedAesUtils = mockStatic(AesUtils.class)) {
                mockedAesUtils.when(() -> AesUtils.decrypt(TEST_AES_PASSWORD, TEST_AES_SALT)).thenReturn("decryptedPassword");

                // When
                String result = remoteAppUserService.getSfUserId(TEST_USER_ID);

                // Then
                assertNotNull(result);
                assertEquals(TEST_SF_USER_ID, result);
                verify(userRepository, times(1)).getById(TEST_USER_ID);
                verify(saleForceService, times(1)).addSfUser(any(SfUserAddDto.class));
                verify(userRepository, times(1)).update(any(UserDo.class), any(LambdaUpdateWrapper.class));
            }
        }
    }

    // 辅助方法
    private UserDo createMockUserDo() {
        UserDo userDo = new UserDo();
        userDo.setId(TEST_USER_ID);
        userDo.setEmail(TEST_EMAIL);
        userDo.setFirstName(TEST_FIRST_NAME);
        userDo.setLastName(TEST_LAST_NAME);
        userDo.setPhone(TEST_PHONE);
        userDo.setCountry(TEST_COUNTRY);
        userDo.setPassword(TEST_PASSWORD);
        userDo.setAesPassword(TEST_AES_PASSWORD);
        userDo.setAesSalt(TEST_AES_SALT);
        userDo.setAddressLine(TEST_ADDRESS_LINE);
        userDo.setPostCode(TEST_POST_CODE);
        userDo.setAppTypeCode(TEST_APP_TYPE_CODE);
        userDo.setSfUserId(TEST_SF_USER_ID);
        userDo.setCreateTime(LocalDateTime.now());
        userDo.setUpdateTime(LocalDateTime.now());
        return userDo;
    }

    private AppUserPageDto createMockAppUserPageDto() {
        AppUserPageDto dto = new AppUserPageDto();
        dto.setEmail(TEST_EMAIL);
        dto.setUserId(TEST_USER_ID.toString());
        dto.setAppTypeCode(TEST_APP_TYPE_CODE);
        dto.setPageNum(1);
        dto.setPageSize(10);
        return dto;
    }

    private Page<UserDo> createMockPage() {
        Page<UserDo> page = new Page<>(1, 10);
        page.setRecords(Arrays.asList(mockUserDo));
        page.setTotal(1);
        return page;
    }
}
