package com.chervon.usercenter.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.usercenter.domain.model.organization.Organization;
import com.chervon.usercenter.domain.model.organization.OrganizationRepository;
import com.chervon.usercenter.infrastructure.converter.OrganizationConverter;
import com.chervon.usercenter.infrastructure.entity.OrganizationDo;
import com.chervon.usercenter.infrastructure.mapper.OrganizationMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-06-07 17:08
 **/
@Repository
public class OrganizationRepositoryImpl extends ServiceImpl<OrganizationMapper, OrganizationDo>
        implements OrganizationRepository {

    @Override
    public void saveList(List<Organization> list) {
        List<OrganizationDo> organizationDoList = ConvertUtil.convertList(list, OrganizationDo.class);
        this.saveBatch(organizationDoList);
    }

    @Override
    public void deleteList(List<Organization> organizationList) {
        List<OrganizationDo> organizationDoList = ConvertUtil.convertList(organizationList, OrganizationDo.class);
        this.removeBatchByIds(organizationDoList);
    }

    @Override
    public void deleteAll() {
        LambdaQueryWrapper<OrganizationDo> wrapper = new LambdaQueryWrapper<>();
        this.remove(wrapper);
    }

    @Override
    public List<Organization> listAll() {
        LambdaQueryWrapper<OrganizationDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<OrganizationDo> organizationDoList = this.list(lambdaQueryWrapper);
        return ConvertUtil.convertList(organizationDoList, Organization.class);
    }

    @Override
    public void updateList(List<Organization> organizationList) {
        List<OrganizationDo> target = ConvertUtil.convertList(organizationList, OrganizationDo.class);
        this.updateBatchById(target);
    }

    @Override
    public Organization getParent(String parentLdapOrgGuid) {
        LambdaQueryWrapper<OrganizationDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrganizationDo::getLdapOrgGuid, lambdaQueryWrapper);
        OrganizationDo organizationDo = this.getOne(lambdaQueryWrapper);
        return OrganizationConverter.organizationDoConvertOrganization(organizationDo);
    }

    @Override
    public Organization getByGuid(String guid) {
        LambdaQueryWrapper<OrganizationDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrganizationDo::getLdapOrgGuid, guid);
        OrganizationDo organizationDo = this.getOne(lambdaQueryWrapper);
        return OrganizationConverter.organizationDoConvertOrganization(organizationDo);
    }
}
