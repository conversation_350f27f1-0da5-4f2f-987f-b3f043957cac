package com.chervon.iot.middle.api.vo.usage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备当日使用时长
 * 电池包累计充电时长：2026
 * 电池包总放电时长：2031（秒）
 * 当日总充电能量:充电器总充电能量：2027
 * 当日总放电能量：充电器、电池包通用总放电能量：2035
 * <AUTHOR>
 * @since 2024-05-29 10:52
 **/
@Data
public class FleetBatteryDailyUsageVo implements Serializable {
    /**
     * 设备Id
     */
    @ApiModelProperty("设备Id")
    private String deviceId;
    /**
     * 一级分类编码
     */
    @ApiModelProperty("一级分类编码")
    private String firstCategoryCode;
    /**
     * 二级分类编码
     */
    @ApiModelProperty("二级分类编码")
    private String secondCategoryCode;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;
    /**
     * 当日日期：2024/01/10
     */
    @ApiModelProperty("当日日期：2024/01/10")
    private String date;
    /**
     * 电池包累计充电时长：2026
     */
    private Integer min2026;
    /**
     * 电池包累计充电时长：2026
     */
    private Integer max2026;
    /**
     * 当日总放电时长（单位：秒） 电池包总放电时长：2031（秒）
     */
    private Integer min2031;
    /**
     * 当日总放电时长（单位：秒） 电池包总放电时长：2031（秒）
     */
    private Integer max2031;
    /**
     * 当日总充电能量:充电器总充电能量：2027
     */
    private Integer min2027;
    /**
     * 当日总充电能量:充电器总充电能量：2027
     */
    private Integer max2027;
    /**
     * 当日总放电能量：充电器、电池包通用总放电能量：2035
     */
    private Integer min2035;
    /**
     * 当日总放电能量：充电器、电池包通用总放电能量：2035
     */
    private Integer max2035;

}