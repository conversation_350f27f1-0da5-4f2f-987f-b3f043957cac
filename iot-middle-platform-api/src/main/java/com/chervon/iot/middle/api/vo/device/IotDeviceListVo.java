package com.chervon.iot.middle.api.vo.device;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @className DeviceListVo
 * @description 设备列表
 * @date/3/4 10:31
 */
@Data
public class IotDeviceListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<IotDeviceListItemVo> deviceList;

    private String nextToken;

}
