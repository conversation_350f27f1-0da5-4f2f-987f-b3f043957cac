package com.chervon.iot.middle.api.exception;

import com.chervon.common.core.exception.base.BaseException;

/**
 * <AUTHOR>
 * @date 2022/6/26 22:06
 */
public class IotMiddleException extends BaseException {

    private static final long serialVersionUID = 1L;

    private String tempMessage;

    public void setTempMessage(String tempMessage) {
        this.tempMessage = tempMessage;
    }

    IotMiddleException() {
        super();
    }

    public IotMiddleException(String code, Object... args) {
        super("iot-middle-platform", code, args);
    }

    public IotMiddleException(IotMiddleErrorCode code, Object... args) {
        super("iot-middle-platform", code.getCode(), code.getDefaultMessage(), code.getErrorMessage(), args);
    }

    @Override
    public String getMessage() {
        if (tempMessage != null) {
            return this.tempMessage;
        }
        return this.getDefaultMessage() == null ? this.getCode() : String.format(this.getDefaultMessage(), this.getArgs());
    }
}
