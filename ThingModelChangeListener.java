package com.chervon.iot.middle.service.listener;

import com.chervon.iot.middle.domain.constant.DataConstant;
import com.chervon.iot.middle.domain.event.*;
import com.chervon.iot.middle.service.IotDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 物模型变更监听器
 * 负责监听物模型变更事件，同步更新TDengine表结构
 */
@Slf4j
@Component
public class ThingModelChangeListener {
    
    @Autowired
    private IotDataService iotDataService;
    
    /**
     * 监听属性添加事件
     */
    @Async
    @EventListener
    public void handlePropertyAdded(PropertyAddedEvent event) {
        try {
            String productKey = event.getProductKey();
            var property = event.getProperty();
            
            log.info("开始处理属性添加事件: productKey={}, identifier={}", 
                    productKey, property.getIdentifier());
            
            // 为普通工况数据表添加列
            addColumnToTable(productKey, property.getIdentifier(), 
                           property.getDataType().getTdEngineType(), false);
            
            // 为扩展物模型数据表添加列
            addColumnToTable(productKey, property.getIdentifier(), 
                           property.getDataType().getTdEngineType(), true);
            
            log.info("属性添加事件处理完成: productKey={}, identifier={}", 
                    productKey, property.getIdentifier());
            
        } catch (Exception e) {
            log.error("处理属性添加事件失败: productKey={}, error={}", 
                     event.getProductKey(), e.getMessage(), e);
        }
    }
    
    /**
     * 监听服务添加事件
     */
    @Async
    @EventListener
    public void handleServiceAdded(ServiceAddedEvent event) {
        try {
            String productKey = event.getProductKey();
            var service = event.getService();
            
            log.info("开始处理服务添加事件: productKey={}, identifier={}", 
                    productKey, service.getIdentifier());
            
            // 服务统一使用BINARY(200)类型
            addColumnToTable(productKey, service.getIdentifier(), "BINARY(200)", false);
            addColumnToTable(productKey, service.getIdentifier(), "BINARY(200)", true);
            
            log.info("服务添加事件处理完成: productKey={}, identifier={}", 
                    productKey, service.getIdentifier());
            
        } catch (Exception e) {
            log.error("处理服务添加事件失败: productKey={}, error={}", 
                     event.getProductKey(), e.getMessage(), e);
        }
    }
    
    /**
     * 监听事件添加事件
     */
    @Async
    @EventListener
    public void handleEventAdded(EventAddedEvent event) {
        try {
            String productKey = event.getProductKey();
            var eventModel = event.getEvent();
            
            log.info("开始处理事件添加事件: productKey={}, identifier={}", 
                    productKey, eventModel.getIdentifier());
            
            // 事件统一使用BINARY(200)类型
            addColumnToTable(productKey, eventModel.getIdentifier(), "BINARY(200)", false);
            addColumnToTable(productKey, eventModel.getIdentifier(), "BINARY(200)", true);
            
            log.info("事件添加事件处理完成: productKey={}, identifier={}", 
                    productKey, eventModel.getIdentifier());
            
        } catch (Exception e) {
            log.error("处理事件添加事件失败: productKey={}, error={}", 
                     event.getProductKey(), e.getMessage(), e);
        }
    }
    
    /**
     * 监听物模型删除事件
     */
    @Async
    @EventListener
    public void handleThingModelDeleted(ThingModelDeletedEvent event) {
        try {
            String productKey = event.getProductKey();
            String identifier = event.getIdentifier();
            String modelType = event.getModelType();
            
            log.info("开始处理物模型删除事件: productKey={}, identifier={}, type={}", 
                    productKey, identifier, modelType);
            
            // 注意：TDengine不支持删除列，这里只记录日志
            // 实际场景中可能需要考虑数据迁移或标记删除
            log.warn("TDengine不支持删除列，物模型删除仅记录日志: productKey={}, identifier={}", 
                    productKey, identifier);
            
        } catch (Exception e) {
            log.error("处理物模型删除事件失败: productKey={}, error={}", 
                     event.getProductKey(), e.getMessage(), e);
        }
    }
    
    /**
     * 为表添加列
     * 
     * @param productKey 产品Key
     * @param identifier 物模型标识符
     * @param columnType 列类型
     * @param isExpTable 是否为扩展表
     */
    private void addColumnToTable(String productKey, String identifier, String columnType, boolean isExpTable) {
        try {
            String stablePrefix = isExpTable ? DataConstant.STABLE_PRODUCT_LOG_EXP : DataConstant.STABLE_PRODUCT_LOG;
            String stableName = stablePrefix + productKey;
            String columnName = DataConstant.STABLE_IDENTIFIER + identifier;
            
            // 检查表是否存在
            if (!iotDataService.isTableExists(stableName)) {
                log.warn("超级表不存在，跳过添加列: table={}", stableName);
                return;
            }
            
            // 检查列是否已存在
            if (iotDataService.isColumnExists(stableName, columnName)) {
                log.info("列已存在，跳过添加: table={}, column={}", stableName, columnName);
                return;
            }
            
            // 添加列
            iotDataService.addColumnToSTable(stableName, columnName, columnType);
            
            log.info("成功为表添加列: table={}, column={} {}", stableName, columnName, columnType);
            
        } catch (Exception e) {
            log.error("为表添加列失败: productKey={}, identifier={}, isExpTable={}, error={}", 
                     productKey, identifier, isExpTable, e.getMessage(), e);
            throw e;
        }
    }
}
