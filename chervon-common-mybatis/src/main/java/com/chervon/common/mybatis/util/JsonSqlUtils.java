package com.chervon.common.mybatis.util;

import com.chervon.common.core.utils.StringUtils;

import java.text.MessageFormat;

/**
 * Mysql JSON类型字段拼接条件工具类
 * <AUTHOR> 2024/12/5
 */
public class JsonSqlUtils {

    /**
     * json set 更新字段表达式
     * @param field
     * @param key
     * @param value
     * @return
     */
    public static  String jsonSetValue(String field,String key,String value){
        if (StringUtils.isEmpty(value)) {
            return String.format("%s=JSON_REMOVE(%s, '$.%s')", field, field, key);
        }
        return String.format("%s=JSON_SET(%s, '$.%s', '%s')",field,field,key,value);
    }

    /**
     * json等于表达式查询
     * @param field
     * @param key
     * @param value
     * @return
     */
    public static  String jsonQueryEq(String field,String key,String value){
        return String.format("%s->>'$.%s'='%s'",field, key, value);
    }

    /**
     * JSON like 表达式
     * like 表达式：在 String.format 中，%% 用于表示一个 % 字符，因此 %%%s%% 会被替换为 '%title%' 的形式
     * @param field
     * @param key
     * @param value
     * @return
     */
    public static  String jsonQueryLike(String field,String key,String value){
        return String.format("%s->>'$.%s' like '%%%s%%'",field, key, value);
    }

    /**
     * json查询字段表达式
     * 使用 select 方法指定要查询的json内部字段
     * queryWrapper.select("id", JsonSqlUtils.jsonSelectField("name",lang), JsonSqlUtils.jsonSelectField("title",lang));
     * @param field
     * @param key
     * @return
     */
    public static String jsonSelectField(String field,String key){
        return MessageFormat.format("{0}->>''$.{1}'' as {0}",field,key);
        //return String.format("%s->>'$.%s' as %s", field, key, field);
    }
}
