package com.chervon.message.mq.consumer;

import com.chervon.common.core.utils.JsonUtils;
import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.dto.MqMessageDto;
import com.chervon.message.service.pushHandler.MessagePusher;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;

/**
 * RocketMQ消费通用消息类
 * <AUTHOR>
 * @since 2024-09-04
 **/
@Slf4j
@Component
public class MessagePushConsumer {

    @Bean
    Consumer<MqMessageDto<String>> topicCommonMessage() {
        return mqMessageDto -> {
            try {
                log.info("topicCommonMessage消息消费接到新消息 -> {}", mqMessageDto);
                List<MessageDto> messageDtoList = JsonUtils.parseObject(mqMessageDto.getMessageBody(), new TypeReference<List<MessageDto>>() {});
                MessagePusher.start(messageDtoList);
                log.info("topicCommonMessage消息消费推送完成：{}",mqMessageDto.getMessageId());
            } catch (Exception e) {
                log.error("topicCommonMessage消息消费推送失败, message:{} error:{}", mqMessageDto, e.getMessage());
            }
        };
    }
}
