package com.chervon.message.api;

import com.chervon.message.api.dto.MessageDto;
import com.chervon.message.api.enums.MessageTypeEnum;
import com.chervon.message.api.enums.OsType;
import com.chervon.message.api.enums.PushMethodEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

/**
 * @Author：flynn.wang
 * @Date：2024/5/15 9:27
 */
@SpringBootTest
public class RemoteMessageServiceIntegrationTest {

    @Autowired
    private RemoteMessageService remoteMessageService;

    /**
     * feedback消息推送测试
     */
    @Test
    public void testPushFeedbackMessage(){
        List<MessageDto> messages =new ArrayList<>();
        MessageDto messageDto=new MessageDto();
        messageDto.setUserId("1759857344404881409");
        messageDto.setToken("10170561a42a146b8a20587e7fe93e1acd1bfe8ca54fe4ac79af92aacf183f01");
        messageDto.setSystemMessageId("1789952656324001793");
        messageDto.setTitle("Customer Service");
        messageDto.setContent("test reply content");
        messageDto.setPushSwitch(1);
        messageDto.setMessageType(MessageTypeEnum.FEEDBACK_MSG.getValue());
        Map payloadDataMap=new HashMap();
        payloadDataMap.put("rutePath","https://www.baidu.com");
        messageDto.setPayloadData(payloadDataMap);
        messageDto.setDeviceType(OsType.IOS);
        //墓碑、banner
        messageDto.setPushTypes(Arrays.asList(PushMethodEnum.TOMBSTONE.getPushTypes(),PushMethodEnum.BANNER.getPushTypes()));
        messages.add(messageDto);
        remoteMessageService.pushMessage(messages);
    }

    /**
     * 系统消息消息推送测试
     */
    @Test
    public void testPushSysMessage(){
        List<MessageDto> messages =new ArrayList<>();
        MessageDto messageDto=new MessageDto();
        messageDto.setUserId("1759857344404881409");
        messageDto.setToken("10170561a42a146b8a20587e7fe93e1acd1bfe8ca54fe4ac79af92aacf183f01");
        messageDto.setSystemMessageId("1789952656324001793");
        messageDto.setTitle("Customer Service");
        messageDto.setContent("test reply content");
        messageDto.setPushSwitch(1);
        messageDto.setMessageType(MessageTypeEnum.SYS_MSG.getValue());
        Map payloadDataMap=new HashMap();
        payloadDataMap.put("rutePath","https://www.baidu.com");
        messageDto.setPayloadData(payloadDataMap);
        messageDto.setDeviceType(OsType.IOS);
        //墓碑和banner
        messageDto.setPushTypes(Arrays.asList(0,2));
        messages.add(messageDto);
        remoteMessageService.pushMessage(messages);
    }

}
