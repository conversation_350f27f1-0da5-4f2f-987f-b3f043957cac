package com.chervon.message.service.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.message.api.dto.*;
import com.chervon.message.api.enums.*;
import com.chervon.message.api.vo.MessageVo;
import com.chervon.message.domain.entity.UserDeviceMessage;
import com.chervon.message.mapper.UserDeviceMessageMapper;
import com.chervon.message.util.CacheUtil;
import com.chervon.technology.api.dto.FaultMessageResultCountDto;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UserDeviceMessageServiceImpl单元测试类
 * 
 * 测试覆盖范围：
 * 1. 获取用户最后一条消息的各种场景
 * 2. 检查用户未读消息的各种场景
 * 3. 更新消息为已读的各种场景
 * 4. 获取分页消息列表的各种场景
 * 5. 综合查询分页消息列表的各种场景
 * 6. 查询消息列表（不分页）的各种场景
 * 7. 统计消息推送结果数量的各种场景
 * 8. 获取消息详情的各种场景
 * 9. 删除消息的各种场景
 * 10. 根据用户ID删除消息的各种场景
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户设备消息服务实现类单元测试")
class UserDeviceMessageServiceImplTest {

    @Mock
    private UserDeviceMessageMapper baseMapper;

    @InjectMocks
    private UserDeviceMessageServiceImpl userDeviceMessageService;

    // 测试常量
    private static final Long TEST_USER_ID = 123456L;
    private static final String TEST_DEVICE_ID = "TEST_DEVICE_001";
    private static final Long TEST_PRODUCT_ID = 1001L;
    private static final String TEST_UUID = "test-uuid-123";
    private static final String TEST_SYSTEM_MESSAGE_ID = "system-msg-001";
    private static final String TEST_TITLE = "测试消息标题";
    private static final String TEST_CONTENT = "测试消息内容";

    /**
     * 初始化MyBatis-Plus Lambda缓存
     */
    public static void initEntityTableInfo(Class<?>... entityClasses) {
        for (Class<?> entityClass : entityClasses) {
            TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), entityClass);
        }
    }

    @BeforeEach
    void setUp() {
        // 初始化实体类表信息
        initEntityTableInfo(UserDeviceMessage.class);
    }

    /**
     * 创建测试用的设备信息列表
     */
    private List<DeviceInfoDto> createTestDeviceInfoList() {
        List<DeviceInfoDto> deviceInfoList = new ArrayList<>();
        DeviceInfoDto deviceInfo1 = new DeviceInfoDto();
        deviceInfo1.setDeviceId(TEST_DEVICE_ID);
        deviceInfo1.setProductId(TEST_PRODUCT_ID);
        deviceInfoList.add(deviceInfo1);
        
        DeviceInfoDto deviceInfo2 = new DeviceInfoDto();
        deviceInfo2.setDeviceId("TEST_DEVICE_002");
        deviceInfo2.setProductId(1002L);
        deviceInfoList.add(deviceInfo2);
        
        return deviceInfoList;
    }

    /**
     * 创建测试用的用户设备消息
     */
    private UserDeviceMessage createTestUserDeviceMessage() {
        UserDeviceMessage message = new UserDeviceMessage();
        message.setId(1L);
        message.setUserId(TEST_USER_ID);
        message.setDeviceId(TEST_DEVICE_ID);
        message.setProductId(TEST_PRODUCT_ID);
        message.setUuid(TEST_UUID);
        message.setSystemMessageId(TEST_SYSTEM_MESSAGE_ID);
        message.setTitle(TEST_TITLE);
        message.setContent(TEST_CONTENT);
        message.setCreateTime(new Date());
        message.setIfRead(ReadFlagEnum.NO.getType());
        message.setIsDeleted(DeletedFlagEnum.NORMAL.getType());
        message.setAppShow(AppShowEnum.YES.getType());
        message.setPushType(PushMethodEnum.MESSAGE_MANAGE.getPushTypes());
        message.setPushResult(PushResultEnum.SUCCESS.getType());
        return message;
    }

    /**
     * 创建测试用的消息列表
     */
    private List<UserDeviceMessage> createTestMessageList() {
        List<UserDeviceMessage> messages = new ArrayList<>();
        messages.add(createTestUserDeviceMessage());
        
        UserDeviceMessage message2 = createTestUserDeviceMessage();
        message2.setId(2L);
        message2.setUuid("test-uuid-456");
        message2.setDeviceId("TEST_DEVICE_002");
        messages.add(message2);
        
        return messages;
    }

    /**
     * 创建测试用的LastMessageDto
     */
    private LastMessageDto createTestLastMessageDto() {
        LastMessageDto dto = new LastMessageDto();
        dto.setUserId(TEST_USER_ID);
        dto.setDeviceInfoList(createTestDeviceInfoList());
        return dto;
    }

    /**
     * 创建测试用的MessageEditDto
     */
    private MessageEditDto createTestMessageEditDto() {
        MessageEditDto dto = new MessageEditDto();
        dto.setId(1L);
        dto.setUserId(TEST_USER_ID);
        dto.setUuid(TEST_UUID);
        dto.setDeviceId(TEST_DEVICE_ID);
        dto.setSystemMessageId(TEST_SYSTEM_MESSAGE_ID);
        return dto;
    }

    /**
     * 创建测试用的SearchMessageInfoDto
     */
    private SearchMessageInfoDto createTestSearchMessageInfoDto() {
        SearchMessageInfoDto dto = new SearchMessageInfoDto();
        dto.setUserId(TEST_USER_ID);
        dto.setDeviceId(TEST_DEVICE_ID);
        dto.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
        dto.setPageNum(1);
        dto.setPageSize(10);
        return dto;
    }

    /**
     * 创建测试用的SearchMessageDto
     */
    private SearchMessageDto createTestSearchMessageDto() {
        SearchMessageDto dto = new SearchMessageDto();
        dto.setSystemMessageId(TEST_SYSTEM_MESSAGE_ID);
        dto.setUserId(TEST_USER_ID.toString());
        dto.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
        dto.setPageNum(1);
        dto.setPageSize(10);
        return dto;
    }

    /**
     * 创建测试用的MessageDetailDto
     */
    private MessageDetailDto createTestMessageDetailDto() {
        MessageDetailDto dto = new MessageDetailDto();
        dto.setUuid(TEST_UUID);
        dto.setUserId(TEST_USER_ID);
        dto.setDeviceId(TEST_DEVICE_ID);
        dto.setSystemMessageId(TEST_SYSTEM_MESSAGE_ID);
        return dto;
    }

    /**
     * 创建测试用的CountMessageDto
     */
    private CountMessageDto createTestCountMessageDto() {
        CountMessageDto dto = new CountMessageDto();
        dto.setListSystemMessageId(Arrays.asList(TEST_SYSTEM_MESSAGE_ID));
        dto.setMessageType(MessageTypeEnum.DEVICE_MSG.getValue());
        return dto;
    }

    @Nested
    @DisplayName("获取用户最后一条消息测试")
    class GetUserLastMessageTests {

        @Test
        @DisplayName("设备信息列表为空时应返回空列表")
        void testGetUserLastMessage_EmptyDeviceList_ShouldReturnEmptyList() {
            // Given
            LastMessageDto requestDto = new LastMessageDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setDeviceInfoList(Collections.emptyList());

            // When
            List<MessageVo> result = userDeviceMessageService.getUserLastMessage(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("设备信息列表为null时应返回空列表")
        void testGetUserLastMessage_NullDeviceList_ShouldReturnEmptyList() {
            // Given
            LastMessageDto requestDto = new LastMessageDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setDeviceInfoList(null);

            // When
            List<MessageVo> result = userDeviceMessageService.getUserLastMessage(requestDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("缓存中存在数据时应直接返回缓存数据")
        void testGetUserLastMessage_CacheExists_ShouldReturnCachedData() {
            // Given
            LastMessageDto requestDto = createTestLastMessageDto();
            List<MessageVo> cachedMessages = Arrays.asList(new MessageVo());

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                cacheUtilMock.when(() -> CacheUtil.getRedis(anyString())).thenReturn(cachedMessages);

                // When
                List<MessageVo> result = userDeviceMessageService.getUserLastMessage(requestDto);

                // Then
                assertNotNull(result);
                assertEquals(cachedMessages, result);
                verifyNoInteractions(baseMapper);
            }
        }

        @Test
        @DisplayName("缓存中无数据且数据库查询结果为空时应返回空列表")
        void testGetUserLastMessage_NoCacheAndEmptyDbResult_ShouldReturnEmptyList() {
            // Given
            LastMessageDto requestDto = createTestLastMessageDto();

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                cacheUtilMock.when(() -> CacheUtil.getRedis(anyString())).thenReturn(null);
                when(baseMapper.queryDeviceLastMessage(eq(TEST_USER_ID), anyList(), any(Date.class)))
                    .thenReturn(Collections.emptyList());

                // When
                List<MessageVo> result = userDeviceMessageService.getUserLastMessage(requestDto);

                // Then
                assertNotNull(result);
                assertTrue(result.isEmpty());
                verify(baseMapper).queryDeviceLastMessage(eq(TEST_USER_ID), anyList(), any(Date.class));
            }
        }

        @Test
        @DisplayName("缓存中无数据但数据库有数据时应返回转换后的数据并缓存")
        void testGetUserLastMessage_NoCacheButHasDbData_ShouldReturnDataAndCache() {
            // Given
            LastMessageDto requestDto = createTestLastMessageDto();
            List<UserDeviceMessage> dbMessages = createTestMessageList();

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                cacheUtilMock.when(() -> CacheUtil.getRedis(anyString())).thenReturn(null);
                when(baseMapper.queryDeviceLastMessage(eq(TEST_USER_ID), anyList(), any(Date.class)))
                    .thenReturn(dbMessages);

                // When
                List<MessageVo> result = userDeviceMessageService.getUserLastMessage(requestDto);

                // Then
                assertNotNull(result);
                assertEquals(2, result.size());
                assertEquals(TEST_PRODUCT_ID.toString(), result.get(0).getProductId());
                assertEquals(MessageTypeEnum.DEVICE_MSG.getValue(), result.get(0).getMessageType());

                verify(baseMapper).queryDeviceLastMessage(eq(TEST_USER_ID), anyList(), any(Date.class));
                cacheUtilMock.verify(() -> CacheUtil.putRedisExpire(anyString(), eq(result), eq(300L)));
            }
        }
    }

    @Nested
    @DisplayName("检查用户未读消息测试")
    class CheckUserUnReadMessageTests {

        @Test
        @DisplayName("设备信息列表为空时应返回false")
        void testCheckUserUnReadMessage_EmptyDeviceList_ShouldReturnFalse() {
            // Given
            LastMessageDto requestDto = new LastMessageDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setDeviceInfoList(Collections.emptyList());

            // When
            boolean result = userDeviceMessageService.checkUserUnReadMessage(requestDto);

            // Then
            assertFalse(result);
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("设备信息列表为null时应返回false")
        void testCheckUserUnReadMessage_NullDeviceList_ShouldReturnFalse() {
            // Given
            LastMessageDto requestDto = new LastMessageDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setDeviceInfoList(null);

            // When
            boolean result = userDeviceMessageService.checkUserUnReadMessage(requestDto);

            // Then
            assertFalse(result);
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("存在未读消息时应返回true")
        void testCheckUserUnReadMessage_HasUnreadMessages_ShouldReturnTrue() {
            // Given
            LastMessageDto requestDto = createTestLastMessageDto();

            // 模拟count方法返回大于0的值
            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(5L).when(spyService).count(any(LambdaQueryWrapper.class));

            // When
            boolean result = spyService.checkUserUnReadMessage(requestDto);

            // Then
            assertTrue(result);
        }

        @Test
        @DisplayName("不存在未读消息时应返回false")
        void testCheckUserUnReadMessage_NoUnreadMessages_ShouldReturnFalse() {
            // Given
            LastMessageDto requestDto = createTestLastMessageDto();

            // 模拟count方法返回0
            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(0L).when(spyService).count(any(LambdaQueryWrapper.class));

            // When
            boolean result = spyService.checkUserUnReadMessage(requestDto);

            // Then
            assertFalse(result);
        }
    }

    @Nested
    @DisplayName("更新消息为已读测试")
    class UpdateMessageReadTests {

        @Test
        @DisplayName("所有参数都为空时应返回false")
        void testUpdateMessageRead_AllParametersEmpty_ShouldReturnFalse() {
            // Given
            MessageEditDto requestDto = new MessageEditDto();
            requestDto.setUserId(TEST_USER_ID);
            // 所有关键参数都为null或空

            // When
            boolean result = userDeviceMessageService.updateMessageRead(requestDto);

            // Then
            assertFalse(result);
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("有效参数时应成功更新并清除缓存")
        void testUpdateMessageRead_ValidParameters_ShouldUpdateAndClearCache() {
            // Given
            MessageEditDto requestDto = createTestMessageEditDto();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(true).when(spyService).update(any(LambdaUpdateWrapper.class));

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                // When
                boolean result = spyService.updateMessageRead(requestDto);

                // Then
                assertTrue(result);
                cacheUtilMock.verify(() -> CacheUtil.removeRedis(anyString()));
            }
        }

        @Test
        @DisplayName("仅有UUID参数时应成功更新")
        void testUpdateMessageRead_OnlyUuidParameter_ShouldUpdate() {
            // Given
            MessageEditDto requestDto = new MessageEditDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setUuid(TEST_UUID);

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(true).when(spyService).update(any(LambdaUpdateWrapper.class));

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                // When
                boolean result = spyService.updateMessageRead(requestDto);

                // Then
                assertTrue(result);
                cacheUtilMock.verify(() -> CacheUtil.removeRedis(anyString()));
            }
        }

        @Test
        @DisplayName("仅有SystemMessageId参数时应成功更新")
        void testUpdateMessageRead_OnlySystemMessageIdParameter_ShouldUpdate() {
            // Given
            MessageEditDto requestDto = new MessageEditDto();
            requestDto.setUserId(TEST_USER_ID);
            requestDto.setSystemMessageId(TEST_SYSTEM_MESSAGE_ID);

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(true).when(spyService).update(any(LambdaUpdateWrapper.class));

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                // When
                boolean result = spyService.updateMessageRead(requestDto);

                // Then
                assertTrue(result);
                cacheUtilMock.verify(() -> CacheUtil.removeRedis(anyString()));
            }
        }
    }

    @Nested
    @DisplayName("获取分页消息列表测试")
    class GetPageListTests {

        @Test
        @DisplayName("设备ID为空时应抛出ServiceException")
        void testGetPageList_EmptyDeviceId_ShouldThrowException() {
            // Given
            SearchMessageInfoDto pageRequest = createTestSearchMessageInfoDto();
            pageRequest.setDeviceId("");

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> userDeviceMessageService.getPageList(pageRequest));

            assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("设备ID为null时应抛出ServiceException")
        void testGetPageList_NullDeviceId_ShouldThrowException() {
            // Given
            SearchMessageInfoDto pageRequest = createTestSearchMessageInfoDto();
            pageRequest.setDeviceId(null);

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> userDeviceMessageService.getPageList(pageRequest));

            assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("正常参数时应返回分页结果")
        void testGetPageList_ValidParameters_ShouldReturnPageResult() {
            // Given
            SearchMessageInfoDto pageRequest = createTestSearchMessageInfoDto();
            List<UserDeviceMessage> mockMessages = createTestMessageList();
            Page<UserDeviceMessage> mockPage = new Page<>(1, 10);
            mockPage.setRecords(mockMessages);
            mockPage.setTotal(2);
            mockPage.setPages(1);

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(mockPage).when(spyService).page(any(Page.class), any(LambdaQueryWrapper.class));

            // When
            PageResult<MessageVo> result = spyService.getPageList(pageRequest);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(2, result.getTotal());
            assertEquals(1, result.getPages());
            assertEquals(2, result.getList().size());
        }
    }

    @Nested
    @DisplayName("综合查询分页消息列表测试")
    class SearchPageListTests {

        @Test
        @DisplayName("正常参数时应返回分页结果")
        void testSearchPageList_ValidParameters_ShouldReturnPageResult() {
            // Given
            SearchMessageDto pageRequest = createTestSearchMessageDto();
            List<UserDeviceMessage> mockMessages = createTestMessageList();
            Page<UserDeviceMessage> mockPage = new Page<>(1, 10);
            mockPage.setRecords(mockMessages);
            mockPage.setTotal(2);
            mockPage.setPages(1);

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(mockPage).when(spyService).page(any(Page.class), any(LambdaQueryWrapper.class));

            // When
            PageResult<MessageVo> result = spyService.SearchPageList(pageRequest);

            // Then
            assertNotNull(result);
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());
            assertEquals(2, result.getTotal());
            assertEquals(1, result.getPages());
            assertEquals(2, result.getList().size());
        }
    }

    @Nested
    @DisplayName("查询消息列表（不分页）测试")
    class GetListTests {

        @Test
        @DisplayName("正常参数时应返回消息列表")
        void testGetList_ValidParameters_ShouldReturnMessageList() {
            // Given
            SearchMessageDto searchMessageDto = createTestSearchMessageDto();
            List<UserDeviceMessage> mockMessages = createTestMessageList();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(mockMessages).when(spyService).list(any(LambdaQueryWrapper.class));

            // When
            List<MessageVo> result = spyService.getList(searchMessageDto);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals(MessageTypeEnum.DEVICE_MSG.getValue(), result.get(0).getMessageType());
        }

        @Test
        @DisplayName("查询结果为空时应返回空列表")
        void testGetList_EmptyResult_ShouldReturnEmptyList() {
            // Given
            SearchMessageDto searchMessageDto = createTestSearchMessageDto();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(Collections.emptyList()).when(spyService).list(any(LambdaQueryWrapper.class));

            // When
            List<MessageVo> result = spyService.getList(searchMessageDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Nested
    @DisplayName("统计消息推送结果数量测试")
    class CountPushResultNumTests {

        @Test
        @DisplayName("正常参数时应返回统计结果")
        void testCountPushResultNum_ValidParameters_ShouldReturnCountResult() {
            // Given
            CountMessageDto countMessageDto = createTestCountMessageDto();
            List<FaultMessageResultCountDto> mockResult = Arrays.asList(new FaultMessageResultCountDto());

            when(baseMapper.countPushResultNum(countMessageDto)).thenReturn(mockResult);

            // When
            List<FaultMessageResultCountDto> result = userDeviceMessageService.countPushResultNum(countMessageDto);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(baseMapper).countPushResultNum(countMessageDto);
        }
    }

    @Nested
    @DisplayName("获取消息详情测试")
    class GetDetailTests {

        @Test
        @DisplayName("UUID和SystemMessageId都为空时应抛出ServiceException")
        void testGetDetail_BothUuidAndSystemMessageIdEmpty_ShouldThrowException() {
            // Given
            MessageDetailDto messageDetail = new MessageDetailDto();
            messageDetail.setUserId(TEST_USER_ID);

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> userDeviceMessageService.getDetail(messageDetail));

            assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("查询结果为空时应返回null")
        void testGetDetail_EmptyResult_ShouldReturnNull() {
            // Given
            MessageDetailDto messageDetail = createTestMessageDetailDto();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(Collections.emptyList()).when(spyService).list(any(LambdaQueryWrapper.class));

            // When
            MessageVo result = spyService.getDetail(messageDetail);

            // Then
            assertNull(result);
        }

        @Test
        @DisplayName("查询到已读消息时应直接返回")
        void testGetDetail_ReadMessage_ShouldReturnDirectly() {
            // Given
            MessageDetailDto messageDetail = createTestMessageDetailDto();
            UserDeviceMessage message = createTestUserDeviceMessage();
            message.setIfRead(ReadFlagEnum.YES.getType());

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(Arrays.asList(message)).when(spyService).list(any(LambdaQueryWrapper.class));

            // When
            MessageVo result = spyService.getDetail(messageDetail);

            // Then
            assertNotNull(result);
            assertEquals(TEST_PRODUCT_ID.toString(), result.getProductId());
            assertEquals(MessageTypeEnum.DEVICE_MSG.getValue(), result.getMessageType());
        }

        @Test
        @DisplayName("查询到未读消息时应更新为已读并返回")
        void testGetDetail_UnreadMessage_ShouldUpdateToReadAndReturn() {
            // Given
            MessageDetailDto messageDetail = createTestMessageDetailDto();
            UserDeviceMessage message = createTestUserDeviceMessage();
            message.setIfRead(ReadFlagEnum.NO.getType());

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(Arrays.asList(message)).when(spyService).list(any(LambdaQueryWrapper.class));
            doReturn(true).when(spyService).updateMessageRead(any(MessageEditDto.class));

            // When
            MessageVo result = spyService.getDetail(messageDetail);

            // Then
            assertNotNull(result);
            assertEquals(TEST_PRODUCT_ID.toString(), result.getProductId());
            assertEquals(MessageTypeEnum.DEVICE_MSG.getValue(), result.getMessageType());
            assertEquals(ReadFlagEnum.YES.getType(), result.getIfRead());
        }
    }

    @Nested
    @DisplayName("删除消息测试")
    class DeleteTests {

        @Test
        @DisplayName("设备ID为空时应抛出ServiceException")
        void testDelete_EmptyDeviceId_ShouldThrowException() {
            // Given
            MessageDetailDto deleteDetail = new MessageDetailDto();
            deleteDetail.setUserId(TEST_USER_ID);
            deleteDetail.setDeviceId("");

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> userDeviceMessageService.delete(deleteDetail));

            assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("设备ID为null时应抛出ServiceException")
        void testDelete_NullDeviceId_ShouldThrowException() {
            // Given
            MessageDetailDto deleteDetail = new MessageDetailDto();
            deleteDetail.setUserId(TEST_USER_ID);
            deleteDetail.setDeviceId(null);

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> userDeviceMessageService.delete(deleteDetail));

            assertEquals(ErrorCode.PARAMETER_NOT_PROVIDED.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("正常参数时应成功删除并清除缓存")
        void testDelete_ValidParameters_ShouldDeleteAndClearCache() {
            // Given
            MessageDetailDto deleteDetail = createTestMessageDetailDto();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(true).when(spyService).update(any(LambdaUpdateWrapper.class));

            try (MockedStatic<CacheUtil> cacheUtilMock = mockStatic(CacheUtil.class)) {
                // When
                spyService.delete(deleteDetail);

                // Then
                cacheUtilMock.verify(() -> CacheUtil.removeRedis(anyString()));
            }
        }
    }

    @Nested
    @DisplayName("根据用户ID删除消息测试")
    class DeleteByUserIdTests {

        @Test
        @DisplayName("设备信息列表为空时不应执行任何操作")
        void testDeleteByUserId_EmptyDeviceList_ShouldDoNothing() {
            // Given
            Long userId = TEST_USER_ID;
            List<DeviceInfoDto> deviceInfoList = Collections.emptyList();

            // When
            userDeviceMessageService.deleteByUserId(userId, deviceInfoList);

            // Then
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("设备信息列表为null时不应执行任何操作")
        void testDeleteByUserId_NullDeviceList_ShouldDoNothing() {
            // Given
            Long userId = TEST_USER_ID;
            List<DeviceInfoDto> deviceInfoList = null;

            // When
            userDeviceMessageService.deleteByUserId(userId, deviceInfoList);

            // Then
            verifyNoInteractions(baseMapper);
        }

        @Test
        @DisplayName("有效设备信息列表时应批量删除")
        void testDeleteByUserId_ValidDeviceList_ShouldBatchDelete() {
            // Given
            Long userId = TEST_USER_ID;
            List<DeviceInfoDto> deviceInfoList = createTestDeviceInfoList();

            UserDeviceMessageServiceImpl spyService = spy(userDeviceMessageService);
            doReturn(true).when(spyService).update(any(LambdaUpdateWrapper.class));

            // When
            spyService.deleteByUserId(userId, deviceInfoList);

            // Then
            // 验证update方法被调用了设备数量次
            verify(spyService, times(deviceInfoList.size())).update(any(LambdaUpdateWrapper.class));
        }
    }
}
