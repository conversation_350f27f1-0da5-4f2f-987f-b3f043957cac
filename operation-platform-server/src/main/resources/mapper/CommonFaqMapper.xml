<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.CommonFaqMapper">
    <select id="selectManagePage" resultType="com.chervon.operation.domain.dataobject.CommonFaq">
        select t1.* from common_faq as t1 left join faq as t2 on t1.faq_id = t2.id where t1.is_deleted = 0 and
        t2.is_deleted = 0
        <if test="search.instanceId != null and search.instanceId != ''">
            and t2.instance_id like concat('%', #{search.instanceId}, '%')
        </if>
        <if test="search.typeCode != null and search.typeCode != ''">
            and t2.type_code = #{search.typeCode}
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and t1.title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and t1.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and t1.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and t1.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and t1.update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.createBy != null and search.createBy != ''">
            and t1.create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and t1.update_by like concat('%', #{search.updateBy}, '%')
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectManageList" resultType="com.chervon.operation.domain.dataobject.CommonFaq">
        select t1.* from common_faq as t1 left join faq as t2 on t1.faq_id = t2.id where t1.is_deleted = 0 and
        t2.is_deleted = 0
        <if test="search.instanceId != null and search.instanceId != ''">
            and t2.instance_id like concat('%', #{search.instanceId}, '%')
        </if>
        <if test="search.typeCode != null and search.typeCode != ''">
            and t2.type_code = #{search.typeCode}
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and t1.title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and t1.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and t1.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and t1.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and t1.update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.createBy != null and search.createBy != ''">
            and t1.create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and t1.update_by like concat('%', #{search.updateBy}, '%')
        </if>
        order by t1.create_time desc
    </select>



    <select id="selectListIdByGroupName" resultType="long">
        SELECT id
        FROM common_faq
        WHERE is_deleted = 0
          AND CONCAT(',', test_group, ',') REGEXP CONCAT(',', #{groupName}
            , ',')
    </select>

</mapper>