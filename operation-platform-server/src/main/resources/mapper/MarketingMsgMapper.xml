<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.MarketingMsgMapper">
    <select id="selectManagePage" resultType="com.chervon.operation.domain.dataobject.MarketingMsg">
        select * from marketing_msg where is_deleted = 0
        <if test="search.statusCode != null and search.statusCode != ''">
            and status_code = #{search.statusCode}
        </if>
        <if test="search.pushTypeCodes != null">
            and concat(',', push_type_code, ',') REGEXP REPLACE(#{search.pushTypeCodes},',','|')
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.msgId != null and search.msgId != ''">
            and id like concat('%', #{search.msgId}, '%')
        </if>
        <!--创建人,更新人模糊搜索-->
        <if test="search.createBy != null and search.createBy != ''">
            and create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and update_by like concat('%', #{search.updateBy}, '%')
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.pushStartMinTime != null and search.pushStartMinTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &gt;= #{search.pushStartMinTime}
        </if>
        <if test="search.pushStartMaxTime != null and search.pushStartMaxTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &lt;= #{search.pushStartMaxTime}
        </if>
        <if test="search.pushEndMinTime != null and search.pushEndMinTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &gt;= #{search.pushEndMinTime}
        </if>
        <if test="search.pushEndMaxTime != null and search.pushEndMaxTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &lt;= #{search.pushEndMaxTime}
        </if>
        order by create_time desc
    </select>

    <select id="selectReleasePage" resultType="com.chervon.operation.domain.dataobject.MarketingMsg">
        select * from marketing_msg where is_deleted = 0 and status_code != 'will_release'
        <if test="search.applyBy != null and search.applyBy != ''">
            and apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != ''">
            and approved_by like concat('%', #{search.approveBy}, '%')
        </if>
        <if test="search.statusCode != null and search.statusCode != ''">
            and status_code = #{search.statusCode}
        </if>
        <if test="search.pushTypeCodes != null">
            and concat(',', push_type_code, ',') REGEXP REPLACE(#{search.pushTypeCodes},',','|')
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.msgId != null and search.msgId != ''">
            and id like concat('%', #{search.msgId}, '%')
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and approved_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and approved_time &lt;= #{search.approveEndTime}
        </if>
        <if test="search.pushStartMinTime != null and search.pushStartMinTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &gt;= #{search.pushStartMinTime}
        </if>
        <if test="search.pushStartMaxTime != null and search.pushStartMaxTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &lt;= #{search.pushStartMaxTime}
        </if>
        <if test="search.pushEndMinTime != null and search.pushEndMinTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &gt;= #{search.pushEndMinTime}
        </if>
        <if test="search.pushEndMaxTime != null and search.pushEndMaxTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &lt;= #{search.pushEndMaxTime}
        </if>
        order by create_time desc
    </select>

    <select id="selectReleaseList" resultType="com.chervon.operation.domain.dataobject.MarketingMsg">
        select * from marketing_msg where is_deleted = 0 and status_code != 'will_release'
        <if test="search.applyBy != null and search.applyBy != ''">
            and apply_by like concat('%', #{search.applyBy}, '%')
        </if>
        <if test="search.approveBy != null and search.approveBy != ''">
            and approved_by like concat('%', #{search.approveBy}, '%')
        </if>
        <if test="search.statusCode != null and search.statusCode != ''">
            and status_code = #{search.statusCode}
        </if>
        <if test="search.pushTypeCodes != null">
            and concat(',', push_type_code, ',') REGEXP REPLACE(#{search.pushTypeCodes},',','|')
        </if>
        <if test="titleLangIds != null and titleLangIds.size() > 0">
            and title_lang_id in
            <foreach collection="titleLangIds" item="titleLangId" open="(" separator="," close=")">
                #{titleLangId}
            </foreach>
        </if>
        <if test="search.msgId != null and search.msgId != ''">
            and id like concat('%', #{search.msgId}, '%')
        </if>
        <if test="search.applyStartTime != null and search.applyStartTime != ''">
            and apply_time &gt;= #{search.applyStartTime}
        </if>
        <if test="search.applyEndTime != null and search.applyEndTime != ''">
            and apply_time &lt;= #{search.applyEndTime}
        </if>
        <if test="search.approveStartTime != null and search.approveStartTime != ''">
            and approved_time &gt;= #{search.approveStartTime}
        </if>
        <if test="search.approveEndTime != null and search.approveEndTime != ''">
            and approved_time &lt;= #{search.approveEndTime}
        </if>
        <if test="search.pushStartMinTime != null and search.pushStartMinTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &gt;= #{search.pushStartMinTime}
        </if>
        <if test="search.pushStartMaxTime != null and search.pushStartMaxTime != ''">
            and ADDDATE(start_time,INTERVAL -start_zone HOUR) &lt;= #{search.pushStartMaxTime}
        </if>
        <if test="search.pushEndMinTime != null and search.pushEndMinTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &gt;= #{search.pushEndMinTime}
        </if>
        <if test="search.pushEndMaxTime != null and search.pushEndMaxTime != ''">
            and ADDDATE(end_time,INTERVAL -end_zone HOUR) &lt;= #{search.pushEndMaxTime}
        </if>
        order by create_time desc
    </select>

    <select id="selectListIdByGroupName" resultType="long">
        SELECT id
        FROM marketing_msg
        WHERE is_deleted = 0
          AND (
            CONCAT(',', prd_group, ',') REGEXP CONCAT( ',', #{groupName}, ',')
                   OR CONCAT(',', test_group, ',') REGEXP CONCAT(',', #{groupName}, ',')
            );
    </select>

    <update id="updateMsgCount" parameterType="com.chervon.operation.api.dto.MessagePushResultCountDto">
        update marketing_msg
        <set>
            <if test="messagePushResultCountDto.successNum != 0 and updateType=0">
                push_success_num = push_success_num + #{messagePushResultCountDto.successNum},
            </if>
            <if test="messagePushResultCountDto.successNum != 0 and updateType=1">
                push_success_num = #{messagePushResultCountDto.successNum},
            </if>
            <if test="messagePushResultCountDto.failNum != 0 and updateType=0">
                push_fail_num = push_fail_num + #{messagePushResultCountDto.failNum},
            </if>
            <if test="messagePushResultCountDto.failNum != 0 and updateType=1">
                push_fail_num = #{messagePushResultCountDto.failNum},
            </if>
        </set>
        <where>
            id = #{messagePushResultCountDto.systemMessageId}
            and is_deleted = 0
        </where>
    </update>

</mapper>