<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.operation.mapper.HelpFaqMapper">
    <select id="selectHelpFaqPage" resultType="com.chervon.operation.domain.dataobject.HelpFaq">
        select * from help_faq hf where hf.is_deleted = 0
        <if test="search.helpFaqId != null and search.helpFaqId != ''">
            and hf.id like concat('%', #{search.helpFaqId}, '%')
        </if>
        <if test="search.createBy != null and search.createBy != ''">
            and hf.create_by like concat('%', #{search.createBy}, '%')
        </if>
        <if test="search.updateBy != null and search.updateBy != ''">
            and hf.update_by like concat('%', #{search.updateBy}, '%')
        </if>
        <if test="search.title != null and search.title != ''">
            and hf.title like concat('%', #{search.title}, '%')
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and hf.create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and hf.create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and hf.update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and hf.update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.model != null and search.model != ''">
            and exists (select id from  help_faq_model hfm where hfm.help_faq_id=hf.id and hfm.commodity_model like concat('%', #{search.model}, '%'))
        </if>
        <if test="search.sourceCode != null and search.sourceCode != ''">
            and hf.source_code = #{search.sourceCode}
        </if>
        <if test="search.appShowCode != null and search.appShowCode != ''">
            and hf.app_show_code = #{search.appShowCode}
        </if>
        order by hf.create_time desc,hf.praise_count desc
    </select>

    <select id="selectAppHelpFaqPage" resultType="com.chervon.operation.domain.dataobject.HelpFaq">
        select *
        from help_faq hf
        where is_deleted = 0
          and  app_show_code = '1'
        <if test="searchContent != null and searchContent != ''">
            and (
            hf.title like concat('%', #{searchContent}, '%')
            or
            hf.answer like concat('%', #{searchContent}, '%')
            or exists (select id from  help_faq_model hfm where hfm.help_faq_id=hf.id and hfm.commodity_model like concat('%', #{searchContent}, '%'))
            )
        </if>
        order by hf.praise_count desc,hf.create_time desc
    </select>

    <select id="selectHelpFaqList" resultType="com.chervon.operation.domain.dataobject.HelpFaq">
        select * from help_faq where is_deleted = 0
        <if test="search.helpFaqId != null and search.helpFaqId != ''">
            and id like concat('%', #{search.helpFaqId}, '%')
        </if>
        <if test="search.title != null and search.title != ''">
            and title like concat('%', #{search.title}, '%')
        </if>
        <if test="search.createStartTime != null and search.createStartTime != ''">
            and create_time &gt;= #{search.createStartTime}
        </if>
        <if test="search.createEndTime != null and search.createEndTime != ''">
            and create_time &lt;= #{search.createEndTime}
        </if>
        <if test="search.updateStartTime != null and search.updateStartTime != ''">
            and update_time &gt;= #{search.updateStartTime}
        </if>
        <if test="search.updateEndTime != null and search.updateEndTime != ''">
            and update_time &lt;= #{search.updateEndTime}
        </if>
        <if test="search.model != null and search.model != ''">
            and model like concat('%',#{search.model},'%')
        </if>
        <if test="search.sourceCode != null and search.sourceCode != ''">
            and source_code = #{search.sourceCode}
        </if>
        <if test="search.appShowCode != null and search.appShowCode != ''">
            and app_show_code = #{search.appShowCode}
        </if>
        order by create_time desc
    </select>

    <select id="recommend" resultType="com.chervon.operation.domain.dataobject.HelpFaq">
        select *
        from help_faq hf
        where is_deleted = 0
             and  app_show_code = '1'
             and exists (
                   select id
                   from help_faq_model hfm
                   where hfm.help_faq_id=hf.id
                     and hfm.commodity_model in
                         <foreach collection="models" item="model" open="(" separator="," close=")">
                             #{model}
                         </foreach>
            )
             and hf.id !=#{helpFaqId}
        order by praise_count desc,create_time desc
        limit 0,4
    </select>

</mapper>