package com.chervon.operation.controller;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.chervon.authority.sdk.annotation.Log;
import com.chervon.authority.sdk.enums.BusinessType;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.technology.api.RemoteSmsChargingService;
import com.chervon.technology.api.core.BaseRemoteReqDto;
import com.chervon.technology.api.dto.charging.SmsChargingDetailPageDto;
import com.chervon.technology.api.dto.charging.SmsChargingPageDto;
import com.chervon.technology.api.vo.charging.SmsChargingDetailExcel;
import com.chervon.technology.api.vo.charging.SmsChargingDetailVo;
import com.chervon.technology.api.vo.charging.SmsChargingExcel;
import com.chervon.technology.api.vo.charging.SmsChargingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/25 14:50
 */
@Api(tags = "短信计费管理")
@RestController
@Slf4j
@RequestMapping("/charging/sms")
public class SmsChargingController {

    @DubboReference
    private RemoteSmsChargingService remoteSmsChargingService;

    @ApiOperation(value = "短信计费分页查询")
    @PostMapping("page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<SmsChargingVo> page(@RequestBody SmsChargingPageDto req) {
        return remoteSmsChargingService.smsChargingPage(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
    }

    @ApiOperation(value = "短信计费导出")
    @PostMapping("export")
    @Log(businessType = BusinessType.EXPORT)
    public void export(@RequestBody SmsChargingPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<SmsChargingExcel> data = remoteSmsChargingService.smsChargingList(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new SmsChargingExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("SmsCharging-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    @ApiOperation(value = "短信计费详情分页查询")
    @PostMapping("detail/page")
    @Log(businessType = BusinessType.VIEW)
    public PageResult<SmsChargingDetailVo> detailPage(@RequestBody SmsChargingDetailPageDto req) {
        return remoteSmsChargingService.smsChargingDetailPage(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
    }

    @ApiOperation(value = "短信计费详情导出")
    @PostMapping("detail/export")
    @Log(businessType = BusinessType.EXPORT)
    public void detailExport(@RequestBody SmsChargingDetailPageDto req, HttpServletResponse response) throws IOException {
        try {
            List<SmsChargingDetailExcel> data = remoteSmsChargingService.smsChargingDetailList(new BaseRemoteReqDto<>(LocaleContextHolder.getLocale(), req));
            if (CollectionUtils.isEmpty(data)) {
                data = new ArrayList<>();
                data.add(new SmsChargingDetailExcel());
            }
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding("UTF-8");
            //进行下载
            String fileName = URLEncoder.encode("SmsChargingDetail-" + new SimpleDateFormat("yyyyMMdd").format(new Date()), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = CsvUtil.getWriter(response.getWriter());
            csvWriter.writeBeans(data);
            csvWriter.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }
}
