package com.chervon.operation.domain.dto.group;

import com.chervon.operation.api.domain.GroupConditionItem;
import com.chervon.operation.api.enums.GroupTypeEnum;
import com.chervon.operation.config.OperationCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @className DeviceGroupDto
 * @description
 * @date 2022/7/12 19:33
 */
@Data
@ApiModel("分组")
public class GroupDto {

    /**
     * 分组名称
     **/
    @ApiModelProperty(value = "分组名称 只允许字母、数字及下划线，最小1位, 最长128位", required = true)
    @NotNull
    @Length(min = 1, max = 128, message = "最小1位, 最长128位")
    @Pattern(regexp = OperationCommon.DEVICE_GROUP_NAME_REGEX, message = "不符合命名规范，只允许字母、数字及下划线")
    private String groupName;

    /**
     * 分组类型, 默认设备分组,枚举值见字典项groupType
     **/
    @ApiModelProperty(value = "分组类型, 默认设备分组,枚举值见字典项groupType")
    private GroupTypeEnum groupType = GroupTypeEnum.DEVICE_GROUP;

    /**
     * 分组条件
     **/
    @ApiModelProperty("分组条件")
    private List<GroupConditionItem> items;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    @Length(max = 2048, message = "最长2048位")
    private String comments;
}
