package com.chervon.operation.domain.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/20 20:06
 */
@Data
@ApiModel(description = "系统消息入参对象")
public class SysMsgDto {

    @ApiModelProperty(value = "系统消息id，新增或复制为空，编辑不为空")
    private Long sysMsgId;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "路由地址")
    private String rutePath;

    @ApiModelProperty(value = "生产分组名称集合")
    private List<String> prdGroup;

    @ApiModelProperty(value = "测试分组名称集合")
    private List<String> testGroup;

    @ApiModelProperty(value = "推送类型code")
    private List<String> pushTypeCodes;

    @ApiModelProperty(value = "推送频率code")
    private String pushRateCode;

    @ApiModelProperty(value = "开始类型 1 立即 2 定时")
    private Integer startType;

    @ApiModelProperty(value = "开始时间时区")
    private String startZone;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束类型  1 永久 2 定时")
    private Integer endType;

    @ApiModelProperty(value = "结束时间时区")
    private String endZone;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "父消息id，新增或编辑为空，复制不为空")
    private Long fromId;

}
