package com.chervon.operation.domain.dto.suggestion;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/30 14:48
 */
@Data
public class SuggestionRead {

    @ExcelProperty("建议ID")
    private String suggestionId;

    @ExcelProperty("处理建议标题")
    private String title;

    @ExcelProperty("处理建议")
    private String content;

    @ExcelProperty("附加内容")
    private String extra;

    public String getSuggestionId() {
        return CsvUtil.unFormat(this.suggestionId);
    }

    public String getTitle() {
        return CsvUtil.unFormat(this.title);
    }

    public String getContent() {
        return CsvUtil.unFormat(this.content);
    }

    public String getExtra() {
        return CsvUtil.unFormat(this.extra);
    }
}
