package com.chervon.operation.domain.dto.faq.help;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/17 12:14
 */
@Data
@ApiModel(description = "帮助中心faq请求对象")
public class HelpFaqDto {

    @ApiModelProperty(value = "问题id")
    private Long helpFaqId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "商品型号")
    private List<String> model;

    @ApiModelProperty(value = "帮助内容")
    private String answer;

}
