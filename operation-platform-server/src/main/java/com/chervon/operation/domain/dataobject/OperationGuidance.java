package com.chervon.operation.domain.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chervon.common.mybatis.config.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作指导
 *
 * <AUTHOR>
 * @date 2022/8/19 15:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("operation_guidance")
public class OperationGuidance extends BaseDo {

    /**
     * 文件类型 1 本地文件 2 s3
     */
    private String typeCode;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件名称多语言id
     */
    private Long nameLangId;

    /**
     * 文件名称多语言code
     */
    private String nameLangCode;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 文件描述多语言id
     */
    private Long descriptionLangId;

    /**
     * 文件描述多语言code
     */
    private String descriptionLangCode;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 上传的文件名
     */
    private String uploadFileName;

    /**
     * 实体id
     */
    private Long instanceId;

}
