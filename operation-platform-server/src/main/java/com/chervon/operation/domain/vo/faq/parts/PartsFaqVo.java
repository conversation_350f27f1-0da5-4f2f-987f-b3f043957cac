package com.chervon.operation.domain.vo.faq.parts;

import com.chervon.operation.domain.vo.faq.FaqVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/29 15:00
 */
@Data
@ApiModel(description = "配件faq数据")
public class PartsFaqVo {

    @ApiModelProperty(value = "faq")
    private FaqVo faq;

    @ApiModelProperty(value = "记录id-用于后续接口入参")
    private Long partsFaqId;

}
