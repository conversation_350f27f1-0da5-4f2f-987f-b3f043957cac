package com.chervon.operation.domain.dto.parts.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/23 12:12
 */
@Data
@ApiModel(description = "产品-配件-添加配件")
public class ProductPartsAddDto {

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "配件id集合")
    private List<Long> partsId;

}
