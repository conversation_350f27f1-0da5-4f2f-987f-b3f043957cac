package com.chervon.operation.domain.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/15 20:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "经销商分页请求对象")
public class DealerEuPageDto extends PageRequest implements Serializable {

    @ApiModelProperty(value = "经销商id")
    private String dealerId;

    @ApiModelProperty(value = "所在国家")
    private String countryCode;

    @ApiModelProperty(value = "经销商名称")
    private String title;

    @ApiModelProperty(value = "所在镇")
    private String town;

    @ApiModelProperty(value = "所在地区")
    private String region;

    @ApiModelProperty(value = "所在城市")
    private String city;

    @ApiModelProperty(value = "邮编")
    private String postcode;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumber;

    @ApiModelProperty(value = "经销商邮箱")
    private String email;

}
