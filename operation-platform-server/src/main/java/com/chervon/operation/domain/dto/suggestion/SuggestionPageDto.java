package com.chervon.operation.domain.dto.suggestion;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/11/17 12:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "处理建议分页请求对象")
public class SuggestionPageDto extends PageRequest {

    @ApiModelProperty(value = "建议ID")
    private String suggestionId;

    @ApiModelProperty(value = "标题")
    private String title;

}
