package com.chervon.operation.domain.vo.faq.common;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/17 12:27
 */

@Data
public class CommonFaqExcel {

    @Alias("问题ID")
    private Long instanceId;

    @Alias("问题类型")
    private String type;

    @Alias("问题")
    private String title;

    @Alias("问题多语言ID")
    private Long titleLangId;

    @Alias("解决办法")
    private String answer;

    @<PERSON>as("解决办法多语言ID")
    private Long answerLangId;

    @Alias("创建人")
    private String createBy;

    @Alias("创建时间")
    private String createTime;

    @Alias("修改人")
    private String updateBy;

    @Alias("修改时间")
    private String updateTime;

    public String getInstanceId() {
        return CsvUtil.format(this.instanceId == null ? "" : this.instanceId + "");
    }

    public String getType() {
        return CsvUtil.format(this.type);
    }

    public String getTitle() {
        return CsvUtil.format(this.title);
    }

    public String getTitleLangId() {
        return CsvUtil.format(this.titleLangId == null ? "" : this.titleLangId + "");
    }

    public String getAnswer() {
        return CsvUtil.format(this.answer);
    }

    public String getAnswerLangId() {
        return CsvUtil.format(this.answerLangId == null ? "" : this.answerLangId + "");
    }

    public String getCreateBy() {
        return CsvUtil.format(this.createBy);
    }

    public String getCreateTime() {
        return CsvUtil.format(this.createTime);
    }

    public String getUpdateBy() {
        return CsvUtil.format(this.updateBy);
    }

    public String getUpdateTime() {
        return CsvUtil.format(this.updateTime);
    }
}
