package com.chervon.operation.domain.vo.message;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022年12月18日
 **/
@Data
public class MessageRecordSearchVo extends PageRequest implements Serializable {

	@ApiModelProperty(value = "消息id")
	private String msgId;

	@ApiModelProperty(value = "消息标题")
	private String title;

	@ApiModelProperty(value = "推送方式")
	private String pushTypeCode;

	@ApiModelProperty(value = "消息类型，0系统消息，1营销消息, 2设备消息 , 3反馈消息", required = true)
	@NotNull
	private Integer messageType;

	/**
	 * 产品型号
	 */
	@ApiModelProperty("产品型号")
	private String model;

	/**
	 * 商品型号Model#
	 */
	@ApiModelProperty("商品型号Model#")
	private String commodityModel;

}
