package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dataobject.CommonOperationGuidance;
import com.chervon.operation.domain.dto.CommonOperationProductDto;
import com.chervon.operation.domain.dto.CommonOperationProductPageDto;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceDto;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceManagePageDto;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceOperationDto;
import com.chervon.operation.domain.dto.operationguidance.common.CommonOperationGuidanceReleasePageDto;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceExcel;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceManagePageVo;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceReleasePageVo;
import com.chervon.operation.domain.vo.operationguidance.common.CommonOperationGuidanceVo;
import com.chervon.technology.api.vo.CommonProductVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 11:20
 */
public interface CommonOperationGuidanceService extends IService<CommonOperationGuidance> {

    /**
     * 内容管理-通用操作指导-分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<CommonOperationGuidanceManagePageVo> managePage(CommonOperationGuidanceManagePageDto req);


    /**
     * 内容管理-通用操作指导-新增
     *
     * @param req 新增对象
     */
    void add(CommonOperationGuidanceDto req);

    /**
     * 内容管理-通用操作指导-编辑
     *
     * @param req 编辑对象
     */
    void edit(CommonOperationGuidanceDto req);

    /**
     * 内容管理-通用操作指导-详情
     *
     * @param commonOperationGuidanceId 通用操作指导id
     * @return 通用操作指导
     */
    CommonOperationGuidanceVo detail(Long commonOperationGuidanceId);

    /**
     * 内容管理-通用操作指导-查看已关联产品
     *
     * @param req 查询条件
     * @return 已关联产品
     */
    PageResult<CommonProductVo> productPage(CommonOperationProductPageDto req);

    /**
     * 内容管理-通用操作指导-新增关联产品
     *
     * @param req 操作对象
     */
    void productAdd(CommonOperationProductDto req);

    /**
     * 内容管理-通用操作指导-删除关联产品
     *
     * @param req 操作对象
     */
    void productDelete(CommonOperationProductDto req);

    /**
     * 内容管理-通用操作指导-删除
     *
     * @param commonOperationGuidanceId 通用操作指导id
     */
    void delete(Long commonOperationGuidanceId);

    /**
     * 内容管理-通用操作指导-下载
     *
     * @param commonOperationGuidanceId 通用操作指导id
     * @return url
     */
    String download(Long commonOperationGuidanceId);

    /**
     * 导入
     *
     * @param file 文件
     * @return 错误信息
     */
    List<String> importCommonOperationGuidance(MultipartFile file);

    /**
     * 根据条件查询列表数据
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<CommonOperationGuidanceExcel> listData(CommonOperationGuidanceManagePageDto req);

    /**
     * 获取该数据所有已关联产品id集合
     *
     * @param commonOperationGuidanceId 通用操作指导id
     * @return 产品id集合
     */
    List<Long> productIdList(Long commonOperationGuidanceId);
}
