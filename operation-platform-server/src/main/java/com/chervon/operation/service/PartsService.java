package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.operation.domain.dataobject.Parts;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqDto;
import com.chervon.operation.domain.dto.faq.parts.PartsFaqListDto;
import com.chervon.operation.domain.dto.link.parts.PartsLinkDto;
import com.chervon.operation.domain.dto.manual.parts.PartsManualDto;
import com.chervon.operation.domain.dto.operationguidance.parts.PartsOperationGuidanceDto;
import com.chervon.operation.domain.dto.parts.PartsDto;
import com.chervon.operation.domain.dto.parts.PartsEditDto;
import com.chervon.operation.domain.dto.parts.PartsPageDto;
import com.chervon.operation.domain.dto.parts.PartsProductPageDto;
import com.chervon.operation.domain.vo.faq.parts.PartsFaqVo;
import com.chervon.operation.domain.vo.link.parts.PartsLinkVo;
import com.chervon.operation.domain.vo.manual.parts.PartsManualVo;
import com.chervon.operation.domain.vo.operationguidance.parts.PartsOperationGuidanceVo;
import com.chervon.operation.domain.vo.parts.PartsExcel;
import com.chervon.operation.domain.vo.parts.PartsPageVo;
import com.chervon.operation.domain.vo.parts.PartsVo;
import com.chervon.technology.api.vo.CommonProductVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-10
 */
public interface PartsService extends IService<Parts> {

    /**
     * 产品短描述
     */
    String SHORT_DESCRIPTION = "shortDescription";

    /**
     * 产品长描述
     */
    String LONG_DESCRIPTION = "longDescription";

    /**
     * 产品技术规格
     */
    String TECHNICAL_SPECIFICATION = "technicalSpecification";

    /**
     * 分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<PartsPageVo> page(PartsPageDto req);

    /**
     * 新增配件
     *
     * @param req 配件对象
     */
    void add(PartsDto req);

    /**
     * 编辑配件
     *
     * @param req 配件对象
     */
    void edit(PartsDto req);

    /**
     * 配件详情
     *
     * @param partsId 配件id
     * @return 配件数据
     */
    PartsVo detail(Long partsId);

    /**
     * 删除
     *
     * @param partsId 配件id
     */
    void delete(Long partsId);

    /**
     * 关联产品分页查询
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<CommonProductVo> productPage(PartsProductPageDto req);

    /**
     * 导入
     *
     * @param file 文件
     * @return
     */
    List<String> importParts(MultipartFile file);

    /**
     * 列表查询
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<PartsExcel> listData(PartsPageDto req);

    /**
     * 编辑段描述、长描述、技术规格
     *
     * @param req 内容
     */
    void edit(PartsEditDto req);

    /**
     * 编辑用户手册
     *
     * @param req 操作对象
     */
    void manualEdit(PartsManualDto req);

    /**
     * 删除用户手册
     *
     * @param partsManualId 配件用户手册id
     */
    void manualDelete(Long partsManualId);

    /**
     * 下载用户手册
     *
     * @param partsManualId 配件用户手册id
     * @return url
     */
    String manualDownload(Long partsManualId);

    /**
     * 添加用户手册
     *
     * @param req 操作对象
     */
    void manualAdd(PartsManualDto req);

    /**
     * 详情用户手册
     *
     * @param partsManualId 配件用户手册id
     * @return 详情
     */
    PartsManualVo manualDetail(Long partsManualId);

    /**
     * 列表用户手册-用于新增、编辑、删除用户手册后，刷新列表-传partsId
     *
     * @param partsId 配件id
     * @return 列表数据
     */
    List<PartsManualVo> manualList(Long partsId);

    /**
     * 编辑操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceEdit(PartsOperationGuidanceDto req);

    /**
     * 删除操作指导
     *
     * @param partsOperationGuidanceId 配件操作指导id
     */
    void operationGuidanceDelete(Long partsOperationGuidanceId);

    /**
     * 下载操作指导
     *
     * @param partsOperationGuidanceId 配件操作指导id
     * @return url
     */
    String operationGuidanceDownload(Long partsOperationGuidanceId);

    /**
     * 添加操作指导
     *
     * @param req 操作对象
     */
    void operationGuidanceAdd(PartsOperationGuidanceDto req);

    /**
     * 列表操作指导-用于新增、编辑、删除、排序操作指导后，刷新列表-传partsId
     *
     * @param partsId 配件id
     * @return 列表数据
     */
    List<PartsOperationGuidanceVo> operationGuidanceList(Long partsId);

    /**
     * 详情操作指导
     *
     * @param partsOperationGuidanceId 配件操作指导id
     * @return 详情
     */
    PartsOperationGuidanceVo operationGuidanceDetail(Long partsOperationGuidanceId);

    /**
     * 排序操作指导
     *
     * @param partsOperationGuidanceIds 配件操作指导id集合
     */
    void operationGuidanceOrder(List<Long> partsOperationGuidanceIds);

    /**
     * 编辑faq
     *
     * @param req 操作对象
     */
    void faqEdit(PartsFaqDto req);

    /**
     * 删除faq
     *
     * @param partsFaqId 配件faq id
     */
    void faqDelete(Long partsFaqId);

    /**
     * 添加faq
     *
     * @param req 操作对象
     */
    void faqAdd(PartsFaqDto req);

    /**
     * 列表faq-用于新增、编辑、删除、排序faq后，刷新列表-传partsId
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<PartsFaqVo> faqList(PartsFaqListDto req);

    /**
     * 详情faq
     *
     * @param partsFaqId 配件faq id
     * @return 详情
     */
    PartsFaqVo faqDetail(Long partsFaqId);

    /**
     * 排序faq
     *
     * @param partsFaqIds 配件faq id集合
     */
    void faqOrder(List<Long> partsFaqIds);

    /**
     * 编辑链接
     *
     * @param req 链接对象
     */
    void linkEdit(PartsLinkDto req);

    /**
     * 删除链接
     *
     * @param partsLinkId 配件链接id
     */
    void linkDelete(Long partsLinkId);

    /**
     * 新增链接
     *
     * @param req 链接对象
     */
    void linkAdd(PartsLinkDto req);

    /**
     * 列表购买链接-用于新增、编辑、删除、排序购买链接后，刷新列表-传partsId
     *
     * @param partsId 配件id
     * @return 列表数据
     */
    List<PartsLinkVo> linkList(Long partsId);

    /**
     * 配件详情
     *
     * @param partsLinkId 配件链接id
     * @return 配件详情
     */
    PartsLinkVo linkDetail(Long partsLinkId);

}
