package com.chervon.operation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.operation.domain.dataobject.UserManual;
import com.chervon.operation.domain.dto.manual.ManualDto;

/**
 * <AUTHOR>
 * @date 2022/8/19 15:34
 */
public interface UserManualService extends IService<UserManual> {

    /**
     * 新增用户手册
     *
     * @param manual 对象
     * @return 用户手册
     */
    UserManual add(ManualDto manual);

    /**
     * 修改用户手册
     *
     * @param manual 对象
     */
    void edit(ManualDto manual);
}
