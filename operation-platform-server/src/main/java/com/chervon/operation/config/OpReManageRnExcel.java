package com.chervon.operation.config;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/19 16:52
 */
@Data
public class OpReManageRnExcel implements Serializable {

    @Alias("PID")
    private Long productId;

    @Alias("品类名称")
    private String categoryName;

    @Alias("产品型号")
    private String model;

    @Alias("品牌名称")
    private String brandName;

    @Alias("商品型号/Model #")
    private String commodityModel;

    @Alias("RN包名称")
    private String rnName;

    @Alias("RN包版本号")
    private String rnVersion;

    @Alias("APP ID")
    private String appId;

    @Alias("APP名称")
    private String appName;

    @Alias("Android最低兼容版本号")
    private String versionMinAndroid;

    @Alias("ios最低兼容版本号")
    private String versionMinIos;

    @Alias("发布状态")
    private String releaseStatus;

    @Alias("申请人")
    private String applyBy;

    @Alias("申请时间")
    private String applyTime;

    @Alias("审批人")
    private String approvedBy;

    @Alias("审批时间")
    private String approvedTime;

    @Alias("备注")
    private String remark;

    public String getProductId() {
        return CsvUtil.format(this.productId == null ? "" : this.productId + "");
    }

    public String getCategoryName() {
        return CsvUtil.format(this.categoryName);
    }

    public String getModel() {
        return CsvUtil.format(this.model);
    }

    public String getBrandName() {
        return CsvUtil.format(this.brandName);
    }

    public String getCommodityModel() {
        return CsvUtil.format(this.commodityModel);
    }

    public String getRnName() {
        return CsvUtil.format(this.rnName);
    }

    public String getRnVersion() {
        return CsvUtil.format(this.rnVersion);
    }

    public String getAppId() {
        return CsvUtil.format(this.appId);
    }

    public String getAppName() {
        return CsvUtil.format(this.appName);
    }

    public String getVersionMinAndroid() {
        return CsvUtil.format(this.versionMinAndroid);
    }

    public String getVersionMinIos() {
        return CsvUtil.format(this.versionMinIos);
    }

    public String getReleaseStatus() {
        return CsvUtil.format(this.releaseStatus);
    }

    public String getApplyBy() {
        return CsvUtil.format(this.applyBy);
    }

    public String getApplyTime() {
        return CsvUtil.format(this.applyTime);
    }

    public String getApprovedBy() {
        return CsvUtil.format(this.approvedBy);
    }

    public String getApprovedTime() {
        return CsvUtil.format(this.approvedTime);
    }

    public String getRemark() {
        return CsvUtil.format(this.remark);
    }
}
